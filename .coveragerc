# Coverage configuration to control coverage.py

[run]
branch = True
omit =
    # omit everything in the virtual environment
    ./.venv/*
    # omit the visual studio code directory
    ./.vscode/*
    # omit the unit tests from the coverage
    ./test/*
    ./tests/*
    ./app/api/tests/*
    ./app/common/tests/*
    ./app/task_processor/tests/*
    ./*/__pycache__/*
    */__init__.py

dynamic_context = test_function

[report]
fail_under = 90.0
precision = 2

[html]
show_contexts = true
