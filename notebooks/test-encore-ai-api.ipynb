{"cells": [{"cell_type": "code", "execution_count": 287, "id": "643054da-4699-4c7e-8ef4-7466780c0888", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "fb0e610e-8665-49a7-9f4b-968e787b1d9b", "metadata": {}, "outputs": [], "source": ["# local\n", "# base_url = \"http://localhost:8008/api/encore/v1\"\n", "\n", "# dev\n", "base_url = \"https://app-internal.us-east-1.dev-alpha.acastacks.com/api/encore/v1\""]}, {"cell_type": "code", "execution_count": null, "id": "d865f6e1-f0da-4731-a550-67f940f9fe2f", "metadata": {}, "outputs": [], "source": ["token= \"Bearer <your_api_key>\"\n", "\n", "headers = {\n", "    'Authorization': token,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ca4bf73d-eda7-47f4-a2ad-9e0aa84e3fd0", "metadata": {}, "outputs": [], "source": ["# liveness check\n", "r = requests.get(f\"{base_url}/health/liveness\", headers=headers)\n", "print(r.text)\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb1f7e0f-0563-4328-ba51-a6ddf4b26dbe", "metadata": {"scrolled": true}, "outputs": [], "source": ["# readiness check\n", "r = requests.get(f\"{base_url}/health/readiness\", headers=headers)\n", "\n", "print(json.dumps(r.json(), indent=2))"]}, {"cell_type": "code", "execution_count": null, "id": "feef5585-01ab-4ecd-9531-5da28a71ac9d", "metadata": {"scrolled": true}, "outputs": [], "source": ["# list models\n", "r = requests.get(f\"{base_url}/models\", headers=headers)\n", "print(r.text)"]}, {"cell_type": "code", "execution_count": null, "id": "6443adba-d080-49da-8e27-ce22fb1bc527", "metadata": {"scrolled": true}, "outputs": [], "source": ["# test chat completion\n", "body = {\n", "    \"messages\": [\n", "        {\n", "          \"role\": \"system\",\n", "          \"content\": \"You are a helpful assistant.\"\n", "        },\n", "        {\n", "          \"role\": \"user\",\n", "          \"content\": \"What's the capital of France?\"\n", "        }\n", "    ],\n", "    \"model\": \"openai:gpt-4o-mini\",\n", "    # \"model\": \"bedrock-claude-3-5-sonnet\",\n", "}\n", "r = requests.post(f\"{base_url}/chat/completions\", headers=headers, json=body)\n", "print(r.json())"]}, {"cell_type": "code", "execution_count": null, "id": "c8683d1d-3ad0-435c-a6f0-cabb1cbc2d62", "metadata": {}, "outputs": [], "source": ["# test text embedding\n", "body = {\n", "    \"model\": \"openai:text-embedding-3-small\",\n", "    \"input\": [\"What's the capital of France?\"]\n", "}\n", "r = requests.post(f\"{base_url}/embeddings\", headers=headers, json=body)\n", "print(r.text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}