{"cells": [{"cell_type": "code", "execution_count": null, "id": "643054da-4699-4c7e-8ef4-7466780c0888", "metadata": {}, "outputs": [], "source": ["import json\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "fb0e610e-8665-49a7-9f4b-968e787b1d9b", "metadata": {}, "outputs": [], "source": ["# local\n", "# base_url = \"http://localhost:8008/api/llm_gateway/v1\"\n", "\n", "# dev\n", "base_url = \"https://app-internal.us-east-1.dev-alpha.acastacks.com/api/llm_gateway/v1\""]}, {"cell_type": "code", "execution_count": null, "id": "d865f6e1-f0da-4731-a550-67f940f9fe2f", "metadata": {}, "outputs": [], "source": ["token= \"Bearer <your_api_key>\" # use master key for initial setup\n", "\n", "headers = {\n", "    'Authorization': token,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2bd24961-8ee8-4f48-adbd-7c2b0e787c81", "metadata": {"scrolled": true}, "outputs": [], "source": ["# read key info\n", "response = requests.get(\n", "    f\"{base_url}/key/info\",\n", "    headers=headers,\n", ")\n", "print(json.dumps(response.json(), indent=2))"]}, {"cell_type": "code", "execution_count": null, "id": "9ef2a162-6306-474d-9f06-dfa76dc4e461", "metadata": {}, "outputs": [], "source": ["# create a new key for the ecomms team\n", "key_creation_payload = {\n", "    \"model_list\": [\"openai:*\"], # Models ecomms can use\n", "    \"budget\": 10.0, # USD\n", "    \"duration\": \"30d\",\n", "    \"metadata\": {\"team_name\": \"ecomms\", \"client_type\": \"internal\"}\n", "}\n", "response = requests.post(\n", "    f\"{base_url}/key/generate\",\n", "    headers=headers,\n", "    data=json.dumps(key_creation_payload)\n", ")\n", "if response.status_code == 200:\n", "    new_key_data = response.json()\n", "    litellm_team_api_key = new_key_data.get(\"key\")\n", "    # Note: The 'key' generated here is what the API Gateway will use to identify\n", "    # the client to LiteLLM for routing and cost tracking.\n", "    print(json.dumps(response.json(), indent=2))\n", "else:\n", "    print(response.text)\n", "    raise Exception(\"Failed to create LiteLLM key\")"]}, {"cell_type": "code", "execution_count": null, "id": "ca4bf73d-eda7-47f4-a2ad-9e0aa84e3fd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"I'm alive!\"\n"]}], "source": ["# liveness check\n", "r = requests.get(f\"{base_url}/health/liveness\", headers=headers)\n", "print(r.text)\n"]}, {"cell_type": "code", "execution_count": null, "id": "357243f7-e225-4006-a7d1-d53c72cf1fde", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"status\": \"connected\",\n", "  \"db\": \"connected\",\n", "  \"cache\": \"local\",\n", "  \"litellm_version\": \"1.74.0\",\n", "  \"success_callbacks\": [\n", "    \"cache\",\n", "    \"sync_deployment_callback_on_success\",\n", "    \"_PROXY_VirtualKeyModelMaxBudgetLimiter\",\n", "    \"_ProxyDBLogger\",\n", "    \"_PROXY_MaxBudgetLimiter\",\n", "    \"_PROXY_MaxParallelRequestsHandler\",\n", "    \"_PROXY_CacheControlCheck\",\n", "    \"_PROXY_LiteLLMManagedFiles\",\n", "    \"ServiceLogging\"\n", "  ],\n", "  \"use_aiohttp_transport\": true,\n", "  \"last_updated\": \"2025-07-09T20:33:00.672485\"\n", "}\n"]}], "source": ["# readiness check\n", "r = requests.get(f\"{base_url}/health/readiness\", headers=headers)\n", "print(json.dumps(r.json(), indent=2))"]}, {"cell_type": "code", "execution_count": null, "id": "e4db8e7d-0904-4560-8bff-8b4bcb27cbd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "[]\n"]}], "source": ["# list teams\n", "r = requests.get(f\"{base_url}/team/list\", headers=headers)\n", "print(r.status_code)\n", "print(json.dumps(r.json(), indent=2))\n"]}, {"cell_type": "code", "execution_count": null, "id": "74637ec4-a1f2-42b4-8701-cd0d7b148378", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'error': {'message': \"{'error': 'Only admin users can query all teams/other teams. Your user role=internal_user'}\", 'type': 'internal_server_error', 'param': 'None', 'code': '401'}}\n"]}], "source": ["# get user info\n", "r = requests.get(f\"{base_url}/user/info\", headers=headers)\n", "print(r.json())"]}, {"cell_type": "code", "execution_count": null, "id": "6443adba-d080-49da-8e27-ce22fb1bc527", "metadata": {}, "outputs": [], "source": ["# test chat completion\n", "body = {\n", "    \"messages\": [\n", "        {\n", "          \"role\": \"system\",\n", "          \"content\": \"You are a helpful assistant.\"\n", "        },\n", "        {\n", "          \"role\": \"user\",\n", "          \"content\": \"What's the capital of France?\"\n", "        }\n", "    ],\n", "    \"model\": \"gpt-4o-mini\",\n", "}\n", "r = requests.post(f\"{base_url}/chat/completions\", headers=headers, json=body)\n", "print(r.json())"]}, {"cell_type": "code", "execution_count": null, "id": "c8683d1d-3ad0-435c-a6f0-cabb1cbc2d62", "metadata": {}, "outputs": [], "source": ["# test text embedding\n", "body = {\n", "    \"model\": \"openai:text-embedding-3-small\",\n", "    \"input\": [\"What's the capital of France?\"]\n", "}\n", "r = requests.post(f\"{base_url}/embeddings\", headers=headers, json=body)\n", "print(r.json())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}