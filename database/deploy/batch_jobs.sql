-- Deploy Aca.Encore.db:batch_jobs to pg

BEGIN;

CREATE TABLE IF NOT EXISTS batch_jobs (
    id UUID PRIMARY KEY,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    client_guid UUID NOT NULL,
    status INTEGER NOT NULL,
    status_description TEXT,
    processed_count INT NOT NULL,
    total_count INT NOT NULL,
    job_params JSONB NOT NULL,
    results JSONB NOT NULL
);

CREATE UNIQUE INDEX "batch_jobs:unique_id" ON public.batch_jobs (id);
CREATE INDEX IF NOT EXISTS  "batch_jobs:idx_id" ON public.batch_jobs (id);
CREATE INDEX IF NOT EXISTS  "batch_jobs:idx_status" ON public.batch_jobs (status);

COMMENT ON TABLE public.batch_jobs IS 'Table for tracking job execution and status';
COMMENT ON COLUMN public.batch_jobs.job_params IS 'JSON containing job parameters and configuration';
COMMENT ON COLUMN public.batch_jobs.results IS 'JSON containing job execution results';

COMMIT;

