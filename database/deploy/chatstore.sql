-- Deploy Aca.Encore.db:chatstore to pg

BEGIN;

CREATE TABLE IF NOT EXISTS chatstore (
    id BIGSERIAL NOT NULL CONSTRAINT chatstore_pk PRIMARY KEY,
    client_guid uuid NOT NULL,
    user_guid uuid NOT NULL,
    message JSON NOT NULL,
    timestamp TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS "chatstore:idx_id" ON chatstore (id);
CREATE INDEX IF NOT EXISTS "chatstore:idx_client_user_timestamp" ON chatstore (client_guid, user_guid, timestamp);

COMMIT;
