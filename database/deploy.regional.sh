#!/bin/bash
set -e

DEPLOY_MODULE="#{Octopus.ProjectGroup.Name}"
DEPLOY_PROJECT="#{Octopus.Project.Name}"
DEPLOY_REGION="#{AwsRegion}"
DEPLOY_ENVIRONMENT="#{StandardizedEnvironment}"
DEPLOY_DATABASE_NAME="encore"
DEPLOY_DATABASE_ROLE_NAME="#{DbUsername}"
DEPLOY_DATABASE_ROLE_PASSWORD="#{DbPassword}"

appRoot="/alpha/$DEPLOY_ENVIRONMENT/encore"

adminCredentials=$(. ./scripts/database/postgres-get-admin-credentials.sh)

# Construct admin connection string from the database secret
dbHost=$(jq -r ".\"host\"" <<< "$adminCredentials")
dbPort=$(jq -r ".\"port\"" <<< "$adminCredentials")
adminUsername=$(jq -r ".\"username\"" <<< "$adminCredentials")
adminPassword=$(jq -r ".\"password\"" <<< "$adminCredentials")
adminConnectionString="postgresql://$adminUsername:$adminPassword@$dbHost:$dbPort"

# Create the regular connection string SSM parameter for the application to use
. ./scripts/ssm/set-secure-parameter.sh "${appRoot}/database/ConnectionString" "postgresql://$DEPLOY_DATABASE_ROLE_NAME:$DEPLOY_DATABASE_ROLE_PASSWORD@$dbHost:$dbPort/$DEPLOY_DATABASE_NAME"

echo "Initializing database $DEPLOY_DATABASE_NAME on $dbHost:$dbPort"
. ./scripts/database/postgres-initialize-database.sh "$adminConnectionString/postgres"

psql "$adminConnectionString/$DEPLOY_DATABASE_NAME" -c "GRANT ALL ON SCHEMA public TO $DEPLOY_DATABASE_ROLE_NAME;"
psql "$adminConnectionString/$DEPLOY_DATABASE_NAME" -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DEPLOY_DATABASE_ROLE_NAME;"

echo "Creating extension vector if it does not already exist on $DEPLOY_DATABASE_NAME"
psql "$adminConnectionString/$DEPLOY_DATABASE_NAME" -c "SET client_min_messages TO WARNING; CREATE EXTENSION IF NOT EXISTS vector;"

# Keeping this block for future use when flushing out the encore tables is necessary
# echo "Truncating and deleting encore tables for new data..."
# psql "$adminConnectionString/$DEPLOY_DATABASE_NAME" -f deploy/truncate-delete-tables.sql
# echo "Tables truncated and deleted successfully."

# Deploy any schema changes
docker pull sqitch/sqitch:latest
. ./sqitch.sh deploy --target "db:pg://$DEPLOY_DATABASE_ROLE_NAME:$DEPLOY_DATABASE_ROLE_PASSWORD@$dbHost:$dbPort/$DEPLOY_DATABASE_NAME"
