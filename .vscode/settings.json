{
    "autopep8.args": [
        "--ignore=E402",
        "--max-line-length=140",
        "--recursive",
        "--exclude=.venv",
    ],
    "autopep8.importStrategy": "fromEnvironment",
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "editor.renderWhitespace": "trailing",
    "editor.rulers": [
        140,
    ],
    "editor.tabSize": 4,
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "mypy-type-checker.args": [
        "--config-file=./.mypy.ini",
    ],
    "pylint.importStrategy": "fromEnvironment",
    "pylint.args": [
        "--rcfile=.pylintrc",
    ],
    "python.analysis.typeCheckingMode": "strict",
    "python.testing.pytestArgs": [
        "tests"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    //
    // PYTHON SPECIFIC
    //
    "[python]": {
        "editor.codeActionsOnSave": {
            "source.organizeImports": "never",
        },
        "editor.defaultFormatter": "ms-python.autopep8",
    },
    //
    // TYPESCRIPT SPECIFIC
    //
    "[typescript]": {
        "editor.tabSize": 2,
    },
    "python.analysis.extraPaths": [
        "./app/common/src",
        "./app/api/src",
        "./app/llm_gateway/src",
        "./app/mcp_proxy/src",
        "./app/task_processor/src",
    ],
    "python.autoComplete.extraPaths": [
        "./app/common/src",
        "./app/api/src",
        "./app/llm_gateway/src",
        "./app/mcp_proxy/src",
        "./app/task_processor/src",
    ],
    "cursorpyright.analysis.extraPaths": [
        "./app/common/src",
        "./app/api/src",
        "./app/llm_gateway/src",
        "./app/mcp_proxy/src",
        "./app/task_processor/src"
    ],
    "cursorpyright.analysis.typeCheckingMode": "strict",
}
