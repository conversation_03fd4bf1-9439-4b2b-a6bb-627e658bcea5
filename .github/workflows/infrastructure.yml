name: infrastructure

on:
  pull_request:
    paths:
      - "infrastructure/**"
  push:
    paths:
      - "infrastructure/**"
    branches:
      - master
      - integration
      - "release/*"
      - "hotfix/*"
  workflow_dispatch:
    inputs:
      analyze_code:
        description: "`true` to run code analytics"
        type: choice
        options: ["false", "true"]
        default: "false"
      upload_artifact:
        description: "`true` to publish the package of a non-release build"
        type: choice
        options: ["false", "true"]
        default: "false"
      create_release:
        description: "`true` if an octopus release should be created"
        type: choice
        options: ["false", "true"]
        default: "false"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  ProjectPath: ./infrastructure
  DependencyTrackProjectName: Encore Infrastructure
  OctopusProjectName: Encore Infrastructure

jobs:
  build:
    runs-on: aca-default-small
    if: ${{ github.event_name == 'pull_request' || github.ref_name == 'integration' || github.event.inputs.analyze_code == 'true' }}
    steps:
      - uses: actions/checkout@v4
      - uses: aca-group/github-actions/node/setup-and-ci@v1
        with:
          project_path: ${{ env.ProjectPath }}
      - if: ${{ github.event_name == 'pull_request' }}
        uses: aca-group/github-actions/infrastructure/build-cdk@v1
      - if: ${{ github.ref_name == 'integration' || github.event.inputs.analyze_code == 'true' }}
        uses: aca-group/github-actions/node/dependency-track/analyze@v1
        with:
          package_path: ${{ env.ProjectPath }}/package.json
          project_name: ${{ env.DependencyTrackProjectName }}
  package:
    runs-on: aca-default-minimal
    if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    steps:
      - uses: actions/checkout@v4
      - id: release-details
        uses: aca-group/github-actions/octopus/generate-release-details@v1
      - uses: aca-group/github-actions/deployments/checkout-scripts@v1
        with:
          github_token: ${{ secrets.ACA_SVC_TOKEN }}
          scripts: |
            utilities/wait-for-processes.sh
            cdk/diff.sh
            cdk/deploy.sh
            ssm/set-standard-parameter.sh
            ssm/set-secure-parameter.sh
      - uses: aca-group/github-actions/artifacts/upload@v1
        with:
          script: mkdir -p ./dist && cp -r ${{ env.ProjectPath }}/bin ${{ env.ProjectPath }}/lib ${{ env.ProjectPath }}/*.json ${{ env.ProjectPath }}/*.sh scripts ./dist
          path: dist
    outputs:
      release_channel: ${{ steps.release-details.outputs.release_channel }}
      release_version: ${{ steps.release-details.outputs.release_version }}
      package_version: ${{ steps.release-details.outputs.package_version }}
  release:
    needs: package
    if: ${{ github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    runs-on: aca-default-minimal
    steps:
      - uses: aca-group/github-actions/octopus/create-release@v1
        with:
          project_name: ${{ env.OctopusProjectName }}
          channel: ${{ needs.package.outputs.release_channel }}
          release_version: ${{ needs.package.outputs.release_version }}
          package_version: ${{ needs.package.outputs.package_version }}
