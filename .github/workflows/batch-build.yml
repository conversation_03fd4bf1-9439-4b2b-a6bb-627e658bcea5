name: batch-build

on:
  pull_request:
    paths:
      - "app/common/**"
      - "app/task_processor/**"
  push:
    paths:
      - "app/common/**"
      - "app/task_processor/**"
    branches:
      - master
      - integration
      - release/*
      - hotfix/*
  workflow_dispatch:
    inputs:
      upload_artifact:
        description: "`true` to publish the package of a non-release build"
        type: choice
        options: ["false", "true"]
        default: "false"
      create_release:
        description: "`true` if an octopus release should be created"
        type: choice
        options: ["false", "true"]
        default: "false"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  ProjectPath: ./app/task_processor
  CommonPath: ./app/common
  OctopusProjectName: Encore Batch

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run lint
        uses: astral-sh/ruff-action@v3
        with:
          src: "${{ env.ProjectPath }}/src ${{ env.CommonPath }}/src"

  build:
    runs-on: aca-default-minimal
    if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        run: uv python install

      - id: release-details
        uses: aca-group/github-actions/octopus/generate-release-details@v1
        with:
          script: shopt -s extglob && mkdir -p ./dist && rsync -av ${{ env.ProjectPath }}/Dockerfile ./dist/ && rsync -av ${{ env.ProjectPath }}/deploy*.sh ./dist/ && rsync -av ${{ env.ProjectPath }}/src/task_processor/* ./dist/task_processor/ && rsync -av ${{ env.CommonPath }}/src/common/* ./dist/common/ && rsync -av ${{ env.ProjectPath }}/pyproject.toml ./dist/task_processor/ && rsync -av ${{ env.CommonPath }}/pyproject.toml ./dist/common/ && cp uv.lock ./dist/ && cp pyproject.toml ./dist/
          path: dist
      - if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
        uses: aca-group/github-actions/deployments/checkout-scripts@v1
        with:
          github_token: ${{ secrets.ACA_SVC_TOKEN }}
          scripts: |
            utilities/wait-for-processes.sh
            ecr/cross-region-build-image.sh
            ecr/add-image-tag.sh
            ssm/set-standard-parameter.sh
            ssm/set-secure-parameter.sh
            ecs/force-deployment-and-wait.sh
      - if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
        uses: aca-group/github-actions/artifacts/upload@v1
        with:
          script: shopt -s extglob && mkdir -p ./dist/scripts &&  cp -r ./scripts ./dist && rsync -av ${{ env.ProjectPath }}/Dockerfile ./dist/ && rsync -av ${{ env.ProjectPath }}/deploy*.sh ./dist/ && rsync -av ${{ env.ProjectPath }}/src/task_processor/* ./dist/task_processor/ && rsync -av ${{ env.CommonPath }}/src/common/* ./dist/common/ && rsync -av ${{ env.ProjectPath }}/pyproject.toml ./dist/task_processor/ && rsync -av ${{ env.CommonPath }}/pyproject.toml ./dist/common/ && cp uv.lock ./dist/ && cp pyproject.toml ./dist/ # -av src/* ./dist/
          path: dist
    outputs:
      release_channel: ${{ steps.release-details.outputs.release_channel }}
      release_version: ${{ steps.release-details.outputs.release_version }}
      package_version: ${{ steps.release-details.outputs.package_version }}
  release:
    needs: build
    if: ${{ github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    runs-on: aca-default-minimal
    steps:
      - uses: aca-group/github-actions/octopus/create-release@v1
        with:
          project_name: ${{ env.OctopusProjectName }}
          channel: ${{ needs.build.outputs.release_channel }}
          release_version: ${{ needs.build.outputs.release_version }}
          package_version: ${{ needs.build.outputs.package_version }}
