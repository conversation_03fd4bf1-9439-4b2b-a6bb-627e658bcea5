name: mcp-openapi-build

on:
  workflow_dispatch:
    inputs:
      upload_artifact:
        description: "`true` to publish the package of a non-release build"
        type: choice
        options: ["false", "true"]
        default: "false"
      create_release:
        description: "`true` if an octopus release should be created"
        type: choice
        options: ["false", "true"]
        default: "false"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  ProjectPath: ./mcp-openapi
  OctopusProjectName: MCP OpenAPI

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        uses: actions/checkout@v3
        with:
          repository: conorbranagan/mcp-openapi
          path: ${{ env.ProjectPath }}

      - name: Run lint
        uses: astral-sh/ruff-action@v3
        with:
          src: "./mcp-openapi"

  build:
    runs-on: aca-default-minimal
    if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    steps:
      - name: Checkout source
        uses: actions/checkout@v3
        with:
          repository: conorbranagan/mcp-openapi
          path: ${{ env.ProjectPath }}

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        run: uv python install

      - id: release-details
        uses: aca-group/github-actions/octopus/generate-release-details@v1
        with:
          script: shopt -s extglob && mkdir -p ./dist && rsync -av --exclude='unit_tests' ${{ env.ProjectPath }}/* ./dist/
          path: dist
      - if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
        uses: aca-group/github-actions/deployments/checkout-scripts@v1
        with:
          github_token: ${{ secrets.ACA_SVC_TOKEN }}
          scripts: |
            utilities/wait-for-processes.sh
            ecr/cross-region-build-image.sh
            ecr/add-image-tag.sh
            ssm/set-standard-parameter.sh
            ssm/set-secure-parameter.sh
            ecs/force-deployment-and-wait.sh
      - if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
        uses: aca-group/github-actions/artifacts/upload@v1
        with:
          script: shopt -s extglob && mkdir -p ./dist/scripts &&  cp -r ./scripts ./dist && rsync -av --exclude='unit_tests' ${{ env.ProjectPath }}/* ./dist/
          path: dist
    outputs:
      release_channel: ${{ steps.release-details.outputs.release_channel }}
      release_version: ${{ steps.release-details.outputs.release_version }}
      package_version: ${{ steps.release-details.outputs.package_version }}
  release:
    needs: build
    if: ${{ github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    runs-on: aca-default-minimal
    steps:
      - uses: aca-group/github-actions/octopus/create-release@v1
        with:
          project_name: ${{ env.OctopusProjectName }}
          channel: ${{ needs.build.outputs.release_channel }}
          release_version: ${{ needs.build.outputs.release_version }}
          package_version: ${{ needs.build.outputs.package_version }}
