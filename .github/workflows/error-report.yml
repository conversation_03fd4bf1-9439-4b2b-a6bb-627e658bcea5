name: error-report

on:
  schedule:
    # Run Wednesday @ 9:15 Eastern
    - cron: "15 13 * * 3"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  notify:
    runs-on: aca-default-minimal
    steps:
      - uses: aca-group/github-actions/new-relic/error-report@v1
        with:
          new_relic_api_key: ${{ secrets.NEWRELIC_SVC_APIKEY }}
          teams_webhook_url: ${{ secrets.MS_TEAMS_CONTROL_ROOM_WEBHOOK_URL }}
          service_name: encore-ai
          production_log_partition: Log_Modules_encore
