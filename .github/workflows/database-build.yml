name: database-build

on:
  pull_request:
    paths:
      - "database/**"
  push:
    paths:
      - "database/**"
    branches:
      - master
      - integration
      - release/*
      - hotfix/*
  workflow_dispatch:
    inputs:
      upload_artifact:
        description: "`true` to publish the package of a non-release build"
        type: choice
        options: ["false", "true"]
        default: "false"
      create_release:
        description: "`true` if an octopus release should be created"
        type: choice
        options: ["false", "true"]
        default: "false"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  ProjectPath: ./database
  OctopusProjectName: Encore Database

jobs:
  build:
    runs-on: aca-default-minimal
    if: ${{ github.event.inputs.upload_artifact == 'true' || github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    steps:
      - uses: actions/checkout@v4
      - id: release-details
        uses: aca-group/github-actions/octopus/generate-release-details@v1
      - uses: aca-group/github-actions/deployments/checkout-scripts@v1
        with:
          github_token: ${{ secrets.ACA_SVC_TOKEN }}
          scripts: |
            ssm/set-secure-parameter.sh
            database/postgres-get-admin-credentials.sh
            database/postgres-initialize-database.sh
      - uses: aca-group/github-actions/artifacts/upload@v1
        with:
          script: mkdir -p ./dist && cp -r ./scripts ${{ env.ProjectPath }}/. ./dist
          path: dist
    outputs:
      release_channel: ${{ steps.release-details.outputs.release_channel }}
      release_version: ${{ steps.release-details.outputs.release_version }}
      package_version: ${{ steps.release-details.outputs.package_version }}
  release:
    needs: build
    if: ${{ github.event.inputs.create_release == 'true' || github.ref_protected == true }}
    runs-on: aca-default-minimal
    steps:
      - uses: aca-group/github-actions/octopus/create-release@v1
        with:
          project_name: ${{ env.OctopusProjectName }}
          channel: ${{ needs.build.outputs.release_channel }}
          release_version: ${{ needs.build.outputs.release_version }}
          package_version: ${{ needs.build.outputs.package_version }}
