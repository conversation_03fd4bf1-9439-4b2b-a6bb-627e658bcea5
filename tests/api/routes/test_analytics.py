"""
Unit tests for analytics router endpoints.
"""

import uuid
from datetime import datetime, date
from unittest.mock import <PERSON><PERSON>
from typing import List

import pytest
from fastapi import HTTPException
from sqlalchemy.orm import Session

from common.models.tags import (
    TagUsageSummary,
    TagUsageDetail,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageResponse,
)
from api.routes.analytics import (
    get_tag_summary,
    get_tag_details,
    get_tag_daily_usage,
    get_tag_model_usage,
    get_tag_comprehensive_analytics,
    list_available_tags,
)


@pytest.mark.asyncio
async def test_get_tag_summary_success():
    """Test successful tag summary retrieval."""
    # Mock session and result
    mock_session = Mock(spec=Session)
    mock_result = Mock()
    mock_result.total_spend = 100.50
    mock_result.total_requests = 50
    mock_result.total_tokens = 1000
    mock_result.successful_requests = 45
    mock_result.failed_requests = 5
    mock_result.min_date = date(2024, 1, 1)
    mock_result.max_date = date(2024, 1, 31)

    mock_session.execute.return_value.first.return_value = mock_result

    # Call the function
    result = await get_tag_summary(
        tag="test-tag",
        start_date="2024-01-01",
        end_date="2024-01-31",
        session=mock_session
    )

    # Assertions
    assert isinstance(result, TagUsageSummary)
    assert result.tag == "test-tag"
    assert result.total_spend == 100.50
    assert result.total_requests == 50
    assert result.total_tokens == 1000
    assert result.success_rate == 90.0
    assert result.date_range == "2024-01-01 to 2024-01-31"


@pytest.mark.asyncio
async def test_get_tag_summary_no_data():
    """Test tag summary when no data is found."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.first.return_value = None

    with pytest.raises(HTTPException) as exc_info:
        await get_tag_summary(
            tag="nonexistent-tag",
            session=mock_session
        )

    assert exc_info.value.status_code == 404
    assert "No data found for tag 'nonexistent-tag'" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_tag_summary_null_values():
    """Test tag summary with null values in result."""
    mock_session = Mock(spec=Session)
    mock_result = Mock()
    mock_result.total_spend = None
    mock_result.total_requests = None
    mock_result.total_tokens = None
    mock_result.successful_requests = None
    mock_result.failed_requests = None
    mock_result.min_date = None
    mock_result.max_date = None

    mock_session.execute.return_value.first.return_value = mock_result

    with pytest.raises(HTTPException) as exc_info:
        await get_tag_summary(
            tag="test-tag",
            session=mock_session
        )

    assert exc_info.value.status_code == 404


@pytest.mark.asyncio
async def test_get_tag_summary_database_error():
    """Test tag summary when database error occurs."""
    mock_session = Mock(spec=Session)
    mock_session.execute.side_effect = Exception("Database connection failed")

    with pytest.raises(HTTPException) as exc_info:
        await get_tag_summary(
            tag="test-tag",
            session=mock_session
        )

    assert exc_info.value.status_code == 500
    assert "Failed to fetch tag summary" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_tag_details_success():
    """Test successful tag details retrieval."""
    # Mock session and results
    mock_session = Mock(spec=Session)
    mock_record = Mock()
    mock_record.id = uuid.uuid4()
    mock_record.tag = "test-tag"
    mock_record.date = "2024-01-01"
    mock_record.api_key = "test-api-key"
    mock_record.model = "gpt-4"
    mock_record.model_group = "openai"
    mock_record.custom_llm_provider = "azure"
    mock_record.prompt_tokens = 100
    mock_record.completion_tokens = 50
    mock_record.cache_read_input_tokens = 0
    mock_record.cache_creation_input_tokens = 0
    mock_record.spend = 0.50
    mock_record.api_requests = 1
    mock_record.successful_requests = 1
    mock_record.failed_requests = 0
    mock_record.created_at = datetime(2024, 1, 1, 12, 0, 0)
    mock_record.updated_at = datetime(2024, 1, 1, 12, 0, 0)

    mock_session.execute.return_value.scalars.return_value.all.return_value = [mock_record]

    # Call the function
    result = await get_tag_details(
        tag="test-tag",
        limit=10,
        offset=0,
        session=mock_session
    )

    # Assertions
    assert isinstance(result, List)
    assert len(result) == 1
    assert isinstance(result[0], TagUsageDetail)
    assert result[0].tag == "test-tag"
    assert result[0].model == "gpt-4"
    assert result[0].spend == 0.50


@pytest.mark.asyncio
async def test_get_tag_details_empty_result():
    """Test tag details when no records are found."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.scalars.return_value.all.return_value = []

    result = await get_tag_details(
        tag="test-tag",
        session=mock_session
    )

    assert isinstance(result, List)
    assert len(result) == 0


@pytest.mark.asyncio
async def test_get_tag_details_with_date_filters():
    """Test tag details with date filters."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.scalars.return_value.all.return_value = []

    await get_tag_details(
        tag="test-tag",
        start_date="2024-01-01",
        end_date="2024-01-31",
        session=mock_session
    )

    # Verify that the query was built with date filters
    mock_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_get_tag_daily_usage_success():
    """Test successful daily usage retrieval."""
    mock_session = Mock(spec=Session)
    mock_record = Mock()
    mock_record.date = "2024-01-01"
    mock_record.spend = 10.50
    mock_record.api_requests = 5
    mock_record.total_tokens = 500
    mock_record.successful_requests = 4
    mock_record.failed_requests = 1

    mock_session.execute.return_value.all.return_value = [mock_record]

    result = await get_tag_daily_usage(
        tag="test-tag",
        session=mock_session
    )

    assert isinstance(result, List)
    assert len(result) == 1
    assert isinstance(result[0], TagUsageByDate)
    assert result[0].date == "2024-01-01"
    assert result[0].spend == 10.50
    assert result[0].api_requests == 5
    assert result[0].total_tokens == 500
    assert result[0].success_rate == 80.0


@pytest.mark.asyncio
async def test_get_tag_daily_usage_zero_requests():
    """Test daily usage with zero requests."""
    mock_session = Mock(spec=Session)
    mock_record = Mock()
    mock_record.date = "2024-01-01"
    mock_record.spend = 0.0
    mock_record.api_requests = 0
    mock_record.total_tokens = 0
    mock_record.successful_requests = 0
    mock_record.failed_requests = 0

    mock_session.execute.return_value.all.return_value = [mock_record]

    result = await get_tag_daily_usage(
        tag="test-tag",
        session=mock_session
    )

    assert result[0].success_rate == 0.0


@pytest.mark.asyncio
async def test_get_tag_daily_usage_database_error():
    """Test daily usage when database error occurs."""
    mock_session = Mock(spec=Session)
    mock_session.execute.side_effect = Exception("Database error")

    with pytest.raises(HTTPException) as exc_info:
        await get_tag_daily_usage(
            tag="test-tag",
            session=mock_session
        )

    assert exc_info.value.status_code == 500
    assert "Failed to fetch daily tag usage" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_tag_model_usage_success():
    """Test successful model usage retrieval."""
    mock_session = Mock(spec=Session)
    mock_record = Mock()
    mock_record.model = "gpt-4"
    mock_record.model_group = "openai"
    mock_record.custom_llm_provider = "azure"
    mock_record.total_spend = 25.75
    mock_record.total_requests = 10
    mock_record.total_tokens = 1000
    mock_record.successful_requests = 9
    mock_record.failed_requests = 1

    mock_session.execute.return_value.all.return_value = [mock_record]

    result = await get_tag_model_usage(
        tag="test-tag",
        session=mock_session
    )

    assert isinstance(result, List)
    assert len(result) == 1
    assert isinstance(result[0], TagUsageByModel)
    assert result[0].model == "gpt-4"
    assert result[0].model_group == "openai"
    assert result[0].custom_llm_provider == "azure"
    assert result[0].total_spend == 25.75
    assert result[0].total_requests == 10
    assert result[0].total_tokens == 1000
    assert result[0].success_rate == 90.0


@pytest.mark.asyncio
async def test_get_tag_model_usage_null_values():
    """Test model usage with null values."""
    mock_session = Mock(spec=Session)
    mock_record = Mock()
    mock_record.model = None
    mock_record.model_group = None
    mock_record.custom_llm_provider = None
    mock_record.total_spend = 10.0
    mock_record.total_requests = 5
    mock_record.total_tokens = 500
    mock_record.successful_requests = 4
    mock_record.failed_requests = 1

    mock_session.execute.return_value.all.return_value = [mock_record]

    result = await get_tag_model_usage(
        tag="test-tag",
        session=mock_session
    )

    assert result[0].model == "Unknown"
    assert result[0].model_group == "Unknown"
    assert result[0].custom_llm_provider == "Unknown"


@pytest.mark.asyncio
async def test_get_tag_comprehensive_analytics_success():
    """Test successful comprehensive analytics retrieval."""
    mock_session = Mock(spec=Session)

    # Mock summary result
    mock_summary_result = Mock()
    mock_summary_result.total_spend = 100.0
    mock_summary_result.total_requests = 20
    mock_summary_result.total_tokens = 2000
    mock_summary_result.successful_requests = 18
    mock_summary_result.failed_requests = 2
    mock_summary_result.min_date = date(2024, 1, 1)
    mock_summary_result.max_date = date(2024, 1, 31)
    mock_summary_result.total_records = 20

    # Mock daily result
    mock_daily_result = Mock()
    mock_daily_result.date = "2024-01-01"
    mock_daily_result.spend = 10.0
    mock_daily_result.api_requests = 2
    mock_daily_result.total_tokens = 200
    mock_daily_result.successful_requests = 2
    mock_daily_result.failed_requests = 0

    # Mock model result
    mock_model_result = Mock()
    mock_model_result.model = "gpt-4"
    mock_model_result.model_group = "openai"
    mock_model_result.custom_llm_provider = "azure"
    mock_model_result.total_spend = 50.0
    mock_model_result.total_requests = 10
    mock_model_result.total_tokens = 1000
    mock_model_result.successful_requests = 9
    mock_model_result.failed_requests = 1

    # Configure session to return different results for different calls
    mock_session.execute.side_effect = [
        Mock(first=lambda: mock_summary_result),
        Mock(all=lambda: [mock_daily_result]),
        Mock(all=lambda: [mock_model_result])
    ]

    result = await get_tag_comprehensive_analytics(
        tag="test-tag",
        session=mock_session
    )

    assert isinstance(result, TagUsageResponse)
    assert result.tag == "test-tag"
    assert result.total_records == 20
    assert isinstance(result.summary, TagUsageSummary)
    assert isinstance(result.daily_data, List)
    assert isinstance(result.model_breakdown, List)
    assert len(result.daily_data) == 1
    assert len(result.model_breakdown) == 1


@pytest.mark.asyncio
async def test_get_tag_comprehensive_analytics_no_data():
    """Test comprehensive analytics when no data is found."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.first.return_value = None

    with pytest.raises(HTTPException) as exc_info:
        await get_tag_comprehensive_analytics(
            tag="test-tag",
            session=mock_session
        )

    assert exc_info.value.status_code == 404
    assert "No data found for tag 'test-tag'" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_list_available_tags_success():
    """Test successful tag listing."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.scalars.return_value.all.return_value = [
        "tag1", "tag2", "tag3", None, "tag4"
    ]

    result = await list_available_tags(session=mock_session)

    assert isinstance(result, List)
    assert result == ["tag1", "tag2", "tag3", "tag4"]  # None should be filtered out


@pytest.mark.asyncio
async def test_list_available_tags_empty():
    """Test tag listing when no tags exist."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.scalars.return_value.all.return_value = []

    result = await list_available_tags(session=mock_session)

    assert isinstance(result, List)
    assert len(result) == 0


@pytest.mark.asyncio
async def test_list_available_tags_database_error():
    """Test tag listing when database error occurs."""
    mock_session = Mock(spec=Session)
    mock_session.execute.side_effect = Exception("Database error")

    with pytest.raises(HTTPException) as exc_info:
        await list_available_tags(session=mock_session)

    assert exc_info.value.status_code == 500
    assert "Failed to fetch available tags" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_all_endpoints_with_mock_session():
    """Test that all endpoints can be called with a mock session."""
    mock_session = Mock(spec=Session)

    # Configure mock to return empty results for all endpoints
    mock_session.execute.return_value.first.return_value = None
    mock_session.execute.return_value.scalars.return_value.all.return_value = []
    mock_session.execute.return_value.all.return_value = []

    # Test that all endpoints can be called without raising exceptions
    # (they should handle empty results appropriately)

    # Test summary endpoint
    with pytest.raises(HTTPException) as exc_info:
        await get_tag_summary("test-tag", session=mock_session)
    assert exc_info.value.status_code == 404

    # Test details endpoint
    result = await get_tag_details("test-tag", session=mock_session)
    assert isinstance(result, List)
    assert len(result) == 0

    # Test daily usage endpoint
    result = await get_tag_daily_usage("test-tag", session=mock_session)
    assert isinstance(result, List)
    assert len(result) == 0

    # Test model usage endpoint
    result = await get_tag_model_usage("test-tag", session=mock_session)
    assert isinstance(result, List)
    assert len(result) == 0

    # Test comprehensive analytics endpoint
    with pytest.raises(HTTPException) as exc_info:
        await get_tag_comprehensive_analytics("test-tag", session=mock_session)
    assert exc_info.value.status_code == 404

    # Test list tags endpoint
    result = await list_available_tags(session=mock_session)
    assert isinstance(result, List)
    assert len(result) == 0


@pytest.mark.asyncio
async def test_endpoints_with_date_filters():
    """Test that endpoints properly handle date filters."""
    mock_session = Mock(spec=Session)
    mock_session.execute.return_value.first.return_value = None
    mock_session.execute.return_value.scalars.return_value.all.return_value = []
    mock_session.execute.return_value.all.return_value = []

    # Test that all endpoints accept date filters
    start_date = "2024-01-01"
    end_date = "2024-01-31"

    with pytest.raises(HTTPException):
        await get_tag_summary("test-tag", start_date=start_date, end_date=end_date, session=mock_session)

    await get_tag_details("test-tag", start_date=start_date, end_date=end_date, session=mock_session)
    await get_tag_daily_usage("test-tag", start_date=start_date, end_date=end_date, session=mock_session)
    await get_tag_model_usage("test-tag", start_date=start_date, end_date=end_date, session=mock_session)

    with pytest.raises(HTTPException):
        await get_tag_comprehensive_analytics("test-tag", start_date=start_date, end_date=end_date, session=mock_session)

    # Verify that session.execute was called for each endpoint
    assert mock_session.execute.call_count >= 4
