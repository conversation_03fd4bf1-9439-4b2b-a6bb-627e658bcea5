[project]
name = "encore-ai"
version = "0.1.0"
description = "AI platform workspace"
requires-python = ">=3.11"
dependencies = [
    "litellm>=1.73.1",
    "llama-index>=0.12.44",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "autopep8>=2.3.2",
    "boto3-stubs[s3,sqs,ssm,secretsmanager]>=1.38.36",
    "coverage>=7.8.2",
    "faker>=37.3.0",
    "flake8>=7.2.0",
    "flake8-bandit>=4.1.1",
    "flake8-docstrings>=1.7.0",
    "ipykernel>=6.29.5",
    "isort>=6.0.1",
    "moto>=5.1.5",
    "mypy>=1.16.0",
    "mypy-boto3-s3>=1.38.26",
    "mypy-boto3-sqs>=1.38.0",
    "mypy-boto3-ssm>=1.38.5",
    "mypy-boto3-secretsmanager>=1.38.0",
    "pep8-naming>=0.15.1",
    "pylint>=3.3.7",
    "pylint-pydantic>=0.3.5",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.1.1",
    "pytest-env>=1.1.5",
    "pytest-mock>=3.14.1",
    "ruff>=0.11.11",
]

[tool.uv.workspace]
members = ["app/*"]
