apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: encore
  title: Encore API
  links:
    - url: https://app.dev.compliancealpha.com/api/encore/swagger/index.html
      title: OpenApi (Dev)
      icon: dashboard
    - url: https://app.rc.compliancealpha.com/api/encore/swagger/index.html
      title: OpenApi (RC)
      icon: dashboard
    - url: https://app.stg.compliancealpha.com/api/encore/swagger/index.html
      title: OpenApi (Stage)
      icon: dashboard
    - url: https://app.uat.compliancealpha.com/api/encore/swagger/index.html
      title: OpenApi (UAT)
      icon: dashboard
    - url: https://app.compliancealpha.com/api/encore/swagger/index.html
      title: OpenApi (Prod)
      icon: dashboard
  annotations:
    sonarqube.org/project-key: encore
    octopus.com/project-id: Spaces-42/Projects-XX
  tags:
    - python
    - fastapi
    - ai
    - regional
spec:
  type: openapi
  lifecycle: production
  owner: alpha-engineering
  system: compliance-alpha
  definition:
    $text: https://app.dev.compliancealpha.com/api/encore/swagger/common/swagger.json

---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: encore-db
  title: Encore Db
  description: Encore Database
  links: []
  tags:
    - postgresql
    - ai
    - regional
annotations:
    backstage.io/techdocs-ref: dir:.
    octopus.com/project-id: Spaces-42/Projects-XX
spec:
  type: database
  owner: alpha-engineering
  system: compliance-alpha

---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: encore-infrastructure
  title: Encore Infrastructure
  tags:
    - encore
    - ai
    - cdk
  annotations:
    backstage.io/techdocs-ref: dir:.
    octopus.com/project-id: Spaces-42/Projects-XX
spec:
  type: service
  lifecycle: experimental
  owner: alpha-engineering
  system: compliance-alpha

---

apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: encore-batch
  title: Encore Batch
  links: []
  description: Background task processor for encore
  annotations:
    sonarqube.org/project-key: encore
    octopus.com/project-id: Spaces-42/Projects-XX
  tags:
    - python
    - background-task
    - ai
    - regional
spec:
  type: openapi
  lifecycle: production
  owner: alpha-engineering
  system: compliance-alpha
  definition:
    $text: https://app.dev.compliancealpha.com/api/encore/swagger/common/swagger.json
