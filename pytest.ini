# aca-ai/pytest.ini
[pytest]
# Add all relevant source folders to the import path
pythonpath =
    ./app/common/src
    ./app/api/src

# Optional: Define all test folders (for multi-module test discovery)
testpaths =
    ./app/common/tests

# Default flags
; TODO: add all the source folders to the coverage
addopts = -v --disable-warnings --cov=app/common/src/common --cov-report=html --cov-report=term-missing
