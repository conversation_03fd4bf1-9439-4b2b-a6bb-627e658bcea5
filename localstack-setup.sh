#!/bin/sh
echo "Initializing localstack s3"

awslocal s3 mb s3://aca-dev-encore-us-east-1

awslocal sqs create-queue --queue-name sqs-dev-encore-task-processor-us-east-1.fifo --attributes '{"FifoQueue":"true"}'
awslocal sqs create-queue --queue-name sqs-dev-encore-task-processor-us-east-1-dlq.fifo --attributes '{"FifoQueue":"true"}'

# aws s3 cp /app/integration_tests/batch_jobs_test_data/ s3://aca-dev-encore-us-east-1/batch-jobs/test-data/ --recursive --endpoint http://localstack:4566

awslocal ssm put-parameter \
    --name "/alpha/dev/encore/llm/LitellmMasterKey" \
    --value "dummy-master-key" \
    --type "String" \
    --description "Master Key for LiteLLM"

awslocal ssm put-parameter \
    --name "/alpha/dev/encore/database/ConnectionString" \
    --value "**********************************************/encore" \
    --type "SecureString" \
    --description "Database URL for LiteLLM"

awslocal ssm put-parameter \
    --name "/alpha/dev/encore/llm/OpenAiApiKey" \
    --value "sk-openai-api-key" \
    --type "SecureString" \
    --description "OpenAI API Key for LiteLLM"

awslocal ssm put-parameter \
    --name "/alpha/dev/encore/llm/GatewayApiKey" \
    --value "sk-gateway-api-key" \
    --type "SecureString" \
    --description "Gateway API Key for LiteLLM"

awslocal ssm put-parameter \
    --name "/alpha/dev/encore/llm/GatewayBaseUrl" \
    --value "http://llm_gateway:8008" \
    --type "String" \
    --description "Gateway Base URL for LiteLLM"

awslocal ssm put-parameter --name /alpha/dev/encore/llm/AzureBaseUrl --value "https://api.openai.azure.com" --type String
awslocal ssm put-parameter --name /alpha/dev/encore/llm/AzureApiVersion --value "2023-05-15" --type String
awslocal ssm put-parameter --name /alpha/dev/encore/llm/DefaultModel --value "gpt-4o" --type String
awslocal ssm put-parameter --name /alpha/dev/encore/llm/OpenAiOrganization --value "dummy-org" --type String

# TODO: uncomment these parameters later and test
# awslocal ssm put-parameter --name /alpha/dev/encore/pipeline/MaxWorkers --value 1 --type String
# awslocal ssm put-parameter --name /alpha/dev/encore/llm/OpenAiProject --value "project-1234567890" --type String
# awslocal ssm put-parameter --name /alpha/dev/encore/llm/AzureApiKey --value "1234567890abcdef1234567890abcdef" --type SecureString
# awslocal ssm put-parameter --name /alpha/dev/encore/llm/AnthropicApiKey --value "1234567890abcdef1234567890abcdef" --type SecureString
# awslocal ssm put-parameter --name /alpha/dev/encore/llm/AzureApiBase --value "https://api.openai.azure.com" --type String
