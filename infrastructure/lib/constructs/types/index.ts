export interface VectorStoreParams {
	readonly schemaName: string;
	readonly tableName: string;
	readonly vectorField: string;
	readonly textField: string;
	readonly metadataField: string;
	readonly primaryKeyField: string;
	readonly embeddingsModelVectorDimension: number;
}


export interface VectorStoreConfig {
	readonly dbName: string;
	readonly dbUsername: string;
	readonly clusterIdentifier: string;
	readonly params: VectorStoreParams;
}