import {
    aws_elasticloadbalancingv2 as elbv2,
    aws_iam as iam,
    aws_sns as sns,
    aws_sqs as sqs,
    Duration,
    Stack,
    StackProps,
    Environment,
} from "aws-cdk-lib";
import { PolicyStatement } from "aws-cdk-lib/aws-iam";
import { CpuArchitecture, CapacityProviderStrategy } from "aws-cdk-lib/aws-ecs";
import {
    AlphaEcsApiDefinition,
    AlphaEcsApiService,
    AlphaEventBusSubscriber,
    EnvironmentalApp,
    getAlphaVpc,
    createAlphaCdnOriginVerifyListenerCondition,
    getAccessPermissions,
    AccessPermission,
} from "aca-cdk-constructs";

export interface ApiProps {
    ecrName: string;
    containerPort: number;
    basePath: string;
    cpu: number;
    memoryLimitMiB: number;
    startupGracePeriod?: Duration;
    deregistrationDelay?: Duration;
    albRulePriority: number;
    minCapacity: number;
    desiredTaskCount: number;
    maxCapacity: number;
    capacityProviderStrategies: CapacityProviderStrategy[];
    targetCpuUtilizationPercent: number | undefined;
    targetMemoryUtilizationPercent: number | undefined;
}

export interface SqsProps {
    taskQueueIdentifier: string;
    taskVisibilityTimeout?: Duration;
    taskRetentionPeriod?: Duration;
    taskDlqRetentionPeriod?: Duration;
    taskDeadLetterQueueMaxReceiveCount?: number;
}

export interface LlmGatewayProps {
    basePath: string;
    ecrName: string;
    containerPort: number;
    cpu: number;
    memoryLimitMiB: number;
    minCapacity: number;
    desiredTaskCount: number;
    maxCapacity: number;
    capacityProviderStrategies: CapacityProviderStrategy[];
    targetCpuUtilizationPercent: number | undefined;
    targetMemoryUtilizationPercent: number | undefined;
    albRulePriority: number;
}

// TODO: Resolve MCP Proxy Service deployment

// export interface McpProxyProps {
//     ecrName: string;
//     containerPort: number;
//     cpu: number;
//     memoryLimitMiB: number;
//     minCapacity: number;
//     desiredTaskCount: number;
//     maxCapacity: number;
//     capacityProviderStrategies: CapacityProviderStrategy[];
//     targetCpuUtilizationPercent: number | undefined;
//     targetMemoryUtilizationPercent: number | undefined;
//     albRulePriority: number;
// }

export interface TaskProcessorProps {
    ecrName: string;
    containerPort: number | 8888; // Not used but required
    cpu: number;
    memoryLimitMiB: number;
    minCapacity: number;
    desiredTaskCount: number;
    maxCapacity: number;
    capacityProviderStrategies: CapacityProviderStrategy[];
    targetCpuUtilizationPercent: number | undefined;
    targetMemoryUtilizationPercent: number | undefined;
}

export interface AppStackProps extends StackProps {
    appId: string;
    env: Environment;
    eventBusFilterPolicy: { [attribute: string]: sns.SubscriptionFilter } | undefined;
    taskRolePolicies: iam.PolicyStatement[];
    kmsKeyAliases: string[];
    apiProps: ApiProps;
    sqsProps: SqsProps;
    taskProcessorProps: TaskProcessorProps;
    llmGatewayProps: LlmGatewayProps;
    // mcpProxyProps: McpProxyProps; // TODO: Resolve MCP Proxy Service deployment
}

export class AppStack extends Stack {

    constructor(scope: EnvironmentalApp, id: string, props: AppStackProps) {
        super(scope, id, props);
        const region = Stack.of(this).region;
        const account = Stack.of(this).account;
        const vpc = getAlphaVpc(this, scope.envName, region);

        const taskQueuePrefix = `sqs-${scope.envName}-${props.sqsProps.taskQueueIdentifier}-${region}`;
        const taskQueueName = `${taskQueuePrefix}.fifo`;
        const taskQueueDlqName = `${taskQueuePrefix}-dlq.fifo`;
        const taskDeadLetterQueue = new sqs.Queue(this, "TaskDeadLetterQueue", {
            queueName: taskQueueDlqName,
            retentionPeriod: props.sqsProps.taskDlqRetentionPeriod || Duration.days(14),
        });

        const taskQueue = new sqs.Queue(this, "TaskQueue", {
            queueName: `${taskQueueName}`,
            fifo: true,
            visibilityTimeout: props.sqsProps.taskVisibilityTimeout || Duration.seconds(300),
            retentionPeriod: props.sqsProps.taskRetentionPeriod || Duration.days(1),
            deadLetterQueue: {
                queue: taskDeadLetterQueue,
                maxReceiveCount: props.sqsProps.taskDeadLetterQueueMaxReceiveCount || 3,
            },
        });

        const apiServiceName = props.apiProps.ecrName.replace(`alpha/`, "");
        const events = new AlphaEventBusSubscriber(this, "AlphaEventBusSubscriber", {
            appId: props.appId,
            envName: scope.envName,
            sqsSubscriptionProps: {
                filterPolicy: props.eventBusFilterPolicy,
            },
        });
        const ssmPolicies = new PolicyStatement({
            actions: [
                "ssm:GetParameter",
                "ssm:GetParameters",
                "ssm:GetParametersByPath",
            ],
            resources: [
                `arn:aws:ssm:${region}:${account}:parameter/alpha/${scope.envName}/encore/*`,
                `arn:aws:ssm:${region}:${account}:parameter/alpha/${scope.envName}/global/Web/*`,
            ],
        });
        const s3Policies = new PolicyStatement({
            actions: getAccessPermissions("kms", [
                AccessPermission.READ,
                AccessPermission.WRITE,
            ]),
            resources: ["*"],
            conditions: {
                "ForAnyValue:StringLike": {
                    "kms:ResourceAliases": props.kmsKeyAliases.map(aliasName => `alias/${aliasName}`),
                },
            },
        });
        const textractPolicy = new PolicyStatement({
            actions: [
                "textract:StartDocumentAnalysis",
                "textract:GetDocumentAnalysis",
                "textract:GetDocumentTextDetection",
                "textract:StartDocumentTextDetection"
            ],
            resources: ["*"],
        });
        const transcribePolicy = new PolicyStatement({
            actions: [
                "transcribe:StartTranscriptionJob",
                "transcribe:GetTranscriptionJob",
            ],
            resources: ["*"],
        });
        const bedrockInvokeModelPolicy = new PolicyStatement({
            actions: ["bedrock:InvokeModel"],
            resources: [
                `arn:aws:bedrock:${region}::foundation-model/*`,
            ],
        });
        const sqsPolicy = new PolicyStatement({
            actions: [
                "sqs:GetQueueAttributes",
                "sqs:GetQueueUrl",
                "sqs:ListDeadLetterSourceQueues",
                "sqs:ListQueues",
                "sqs:ListQueueTags",
                "sqs:SendMessage",
                "sqs:SendMessageBatch",
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:DeleteMessageBatch",
                "sqs:ChangeMessageVisibility",
                "sqs:ChangeMessageVisibilityBatch",
                "sqs:PurgeQueue",
                "sqs:CancelMessageMoveTask",
                "sqs:ListMessageMoveTasks",
                "sqs:SetQueueAttributes",
                "sqs:StartMessageMoveTask",
                "sqs:TagQueue",
                "sqs:UntagQueue",
                "kms:ListAliases",
            ],
            resources: [
                taskQueue.queueArn,
                taskDeadLetterQueue.queueArn,
                "*"
            ],
        });
        const secretsManagerPolicy = new PolicyStatement({
            actions: [
                "secretsmanager:GetSecretValue",
                "secretsmanager:DescribeSecret",
                "secretsmanager:ListSecretVersionIds",
                "secretsmanager:ListSecrets",
                "secretsmanager:GetResourcePolicy",
                "secretsmanager:CreateSecret",
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:GenerateDataKey",
            ],
            resources: [
                `arn:aws:secretsmanager:${region}:${account}:secret:alpha/${scope.envName}/encore/*`,
                `arn:aws:secretsmanager:${region}:${account}:secret:alpha/${scope.envName}/global/Web/*`,
            ]
        });

        // LLM Gateway Service
        const llmGatewayServiceName = props.llmGatewayProps.ecrName.replace(`alpha/`, "");

        const llmGatewayDefinition = new AlphaEcsApiDefinition(this, `LlmGatewayTaskDefinition`, {
            appId: props.appId,
            serviceName: llmGatewayServiceName,
            envName: scope.envName,
            fargateTaskDefinitionProps: {
                family: `${llmGatewayServiceName}-${scope.envName}`,
                cpu: props.llmGatewayProps.cpu,
                memoryLimitMiB: props.llmGatewayProps.memoryLimitMiB,
                runtimePlatform: { cpuArchitecture: CpuArchitecture.X86_64 }, // TODO: Switch to ARM64
            },
            applicationProps: {
                repositoryName: props.llmGatewayProps.ecrName,
                imageTag: scope.envName,
                taskRolePolicies: [
                    ...props.taskRolePolicies,
                    ssmPolicies,
                    bedrockInvokeModelPolicy,
                ],
                containerOptions: {
                    portMappings: [{ name: "http", containerPort: props.llmGatewayProps.containerPort }],
                    environment: {
                        env_name: scope.envName,
                        applicationName: llmGatewayServiceName,
                        AWS_REGION: region,
                        SERVER_ROOT_PATH: props.llmGatewayProps.basePath
                    },
                    healthCheck: {
                        command: [
                            "CMD-SHELL",
                            `python -c "import requests; exit() if requests.get('http://localhost:8008/health/readiness').status_code == 200 else exit(1)"`
                        ],
                    },
                },
            },
        });

        const llmGatewayService = new AlphaEcsApiService(this, "LlmGatewayService", {
            appId: props.appId,
            envName: scope.envName,
            vpc: vpc,
            serviceName: llmGatewayServiceName,
            containerPort: props.llmGatewayProps.containerPort,
            taskDefinition: llmGatewayDefinition.taskDefinition,
            minCapacity: props.llmGatewayProps.minCapacity,
            desiredCount: props.llmGatewayProps.desiredTaskCount,
            maxCapacity: props.llmGatewayProps.maxCapacity,
            capacityProviderStrategies: props.llmGatewayProps.capacityProviderStrategies,
            targetCpuUtilizationPercent: props.llmGatewayProps.targetCpuUtilizationPercent,
            targetMemoryUtilizationPercent: props.llmGatewayProps.targetMemoryUtilizationPercent,
            allowRedisConnection: false,
            // Internal ALB only - no external ALB access
            // TODO: IP restrict traffic to only the CIDR ranges of our instances
            albs: [
                {
                    id: "int",
                    priority: props.llmGatewayProps.albRulePriority,
                    conditions: [
                        elbv2.ListenerCondition.pathPatterns([`${props.llmGatewayProps.basePath}/*`]),
                    ],
                    healthCheckPath: "/health/liveness",
                },
            ],
        });
        llmGatewayService.allowPostgresConnection();

        // TODO: to be fixed
        // const llmGatewayInternalAlbDnsName = llmGatewayService.albs[0].loadBalancerDnsName || '';


        const apiDefinition = new AlphaEcsApiDefinition(this, `ApiTaskDefinition`, {
            appId: props.appId,
            serviceName: apiServiceName,
            envName: scope.envName,
            fargateTaskDefinitionProps: {
                family: `${apiServiceName}-${scope.envName}`,
                cpu: props.apiProps.cpu,
                memoryLimitMiB: props.apiProps.memoryLimitMiB,
                runtimePlatform: { cpuArchitecture: CpuArchitecture.X86_64 }, // TODO: Switch to ARM64
            },
            applicationProps: {
                repositoryName: props.apiProps.ecrName,
                imageTag: scope.envName,
                taskRolePolicies: [
                    ...events.generateAccessPolicyStatements(),
                    ...props.taskRolePolicies,
                    s3Policies,
                    ssmPolicies,
                    textractPolicy,
                    sqsPolicy,
                    secretsManagerPolicy,
                ],
                containerOptions: {
                    portMappings: [{ name: "http", containerPort: props.apiProps.containerPort }],
                    environment: {
                        env_name: scope.envName, // TODO: rename env_name to Environment?
                        BASE_PATH: props.apiProps.basePath,
                        applicationName: apiServiceName,
                    },
                    healthCheck: {
                        command: [
                            "CMD-SHELL",
                            `curl --fail http://localhost:${props.apiProps.containerPort}/health/liveness || exit 1`,
                        ],
                    },
                },
            },
        });
        taskQueue.grantSendMessages(apiDefinition.taskDefinition.taskRole);
        taskQueue.grantConsumeMessages(apiDefinition.taskDefinition.taskRole);
        taskDeadLetterQueue.grantSendMessages(apiDefinition.taskDefinition.taskRole);
        taskDeadLetterQueue.grantConsumeMessages(apiDefinition.taskDefinition.taskRole);

        events.queue.grantConsumeMessages(apiDefinition.taskDefinition.taskRole);
        const apiService = new AlphaEcsApiService(this, "ApiService", {
            appId: props.appId,
            envName: scope.envName,
            vpc: vpc,
            serviceName: apiServiceName,
            containerPort: props.apiProps.containerPort,
            taskDefinition: apiDefinition.taskDefinition,
            minCapacity: props.apiProps.minCapacity,
            desiredCount: props.apiProps.desiredTaskCount,
            maxCapacity: props.apiProps.maxCapacity,
            healthCheckGracePeriod: props.apiProps.startupGracePeriod,
            deregistrationDelay: props.apiProps.deregistrationDelay,
            capacityProviderStrategies: props.apiProps.capacityProviderStrategies,
            targetCpuUtilizationPercent: props.apiProps.targetCpuUtilizationPercent,
            targetMemoryUtilizationPercent: props.apiProps.targetMemoryUtilizationPercent,
            allowRedisConnection: true,
            albs: [
                {
                    id: "ext",
                    priority: props.apiProps.albRulePriority,
                    conditions: [
                        createAlphaCdnOriginVerifyListenerCondition(scope.envName),
                        elbv2.ListenerCondition.pathPatterns([`${props.apiProps.basePath}/*`]),
                    ],
                    healthCheckPath: `/health/liveness`,
                },
                {
                    id: "int",
                    priority: props.apiProps.albRulePriority,
                    conditions: [
                        elbv2.ListenerCondition.pathPatterns([`${props.apiProps.basePath}/*`]),
                    ],
                    healthCheckPath: `/health/liveness`,
                },
            ],
        });
        apiService.allowPostgresConnection();

        const taskProcessorServiceName = `encore-batch`;

        const taskProcessorDefinition = new AlphaEcsApiDefinition(this, `taskProcessorTaskDefinition`, {
            appId: props.appId,
            serviceName: taskProcessorServiceName,
            envName: scope.envName,
            fargateTaskDefinitionProps: {
                family: `${taskProcessorServiceName}-${scope.envName}`,
                cpu: props.taskProcessorProps.cpu,
                memoryLimitMiB: props.taskProcessorProps.memoryLimitMiB,
                runtimePlatform: { cpuArchitecture: CpuArchitecture.X86_64 }, // TODO: Switch to ARM64
            },
            applicationProps: {
                repositoryName: props.apiProps.ecrName, // Use the same ECR repository
                imageTag: scope.envName,
                taskRolePolicies: [
                    ...events.generateAccessPolicyStatements(),
                    ...props.taskRolePolicies,
                    s3Policies,
                    ssmPolicies,
                    textractPolicy,
                    transcribePolicy,
                    sqsPolicy,
                ],
                containerOptions: {
                    command: ["sleep", "999999"],
                    portMappings: [{ name: "http", containerPort: props.taskProcessorProps.containerPort }],
                    environment: {
                        env_name: scope.envName,
                        applicationName: taskProcessorServiceName,
                    },
                    healthCheck: {
                        command: [
                            "CMD-SHELL",
                            "pgrep -f task_processor || exit 0"
                        ],
                        startPeriod: Duration.seconds(60),
                        interval: Duration.seconds(30),
                        timeout: Duration.seconds(5),
                        retries: 3
                    },
                },
            },
        });
        taskQueue.grantSendMessages(taskProcessorDefinition.taskDefinition.taskRole);
        taskQueue.grantConsumeMessages(taskProcessorDefinition.taskDefinition.taskRole);
        taskDeadLetterQueue.grantSendMessages(taskProcessorDefinition.taskDefinition.taskRole);
        taskDeadLetterQueue.grantConsumeMessages(taskProcessorDefinition.taskDefinition.taskRole);

        events.queue.grantConsumeMessages(taskProcessorDefinition.taskDefinition.taskRole);

        const taskProcessorService = new AlphaEcsApiService(this, "TaskProcessorService", {
            appId: props.appId,
            envName: scope.envName,
            vpc: vpc,
            serviceName: taskProcessorServiceName,
            containerPort: props.taskProcessorProps.containerPort, // Not used but required
            taskDefinition: taskProcessorDefinition.taskDefinition,
            minCapacity: props.taskProcessorProps.minCapacity,
            desiredCount: props.taskProcessorProps.desiredTaskCount,
            maxCapacity: props.taskProcessorProps.maxCapacity,
            capacityProviderStrategies: props.taskProcessorProps.capacityProviderStrategies,
            targetCpuUtilizationPercent: props.taskProcessorProps.targetCpuUtilizationPercent,
            targetMemoryUtilizationPercent: props.taskProcessorProps.targetMemoryUtilizationPercent,
            allowRedisConnection: true,
            // No ALB configuration for the background worker
            albs: []
        });

        taskProcessorService.allowPostgresConnection();

        // TODO: Resolve MCP Proxy Service deployment

        // Mcp Proxy Service
        // const mcpProxyServiceName = "encore-mcp";

        // const mcpProxyDefinition = new AlphaEcsApiDefinition(this, `McpProxyTaskDefinition`, {
        //     appId: props.appId,
        //     serviceName: mcpProxyServiceName,
        //     envName: scope.envName,
        //     fargateTaskDefinitionProps: {
        //         family: `${mcpProxyServiceName}-${scope.envName}`,
        //         cpu: props.mcpProxyProps.cpu,
        //         memoryLimitMiB: props.mcpProxyProps.memoryLimitMiB,
        //         runtimePlatform: { cpuArchitecture: CpuArchitecture.ARM64 },
        //     },
        //     applicationProps: {
        //         repositoryName: props.mcpProxyProps.ecrName,
        //         imageTag: scope.envName,
        //         taskRolePolicies: [
        //             ...props.taskRolePolicies,
        //             ssmPolicies,
        //         ],
        //         containerOptions: {
        //             portMappings: [{ name: "http", containerPort: props.mcpProxyProps.containerPort }],
        //             environment: {
        //                 env_name: scope.envName,
        //                 applicationName: mcpProxyServiceName,
        //             },
        //             healthCheck: {
        //                 command: [
        //                     "CMD-SHELL",
        //                     `exit 0`,
        //                 ],
        //             },
        //         },
        //     },
        // });

        // const mcpProxyService = new AlphaEcsApiService(this, "McpProxyService", {
        //     appId: props.appId,
        //     envName: scope.envName,
        //     vpc: vpc,
        //     serviceName: mcpProxyServiceName,
        //     containerPort: props.mcpProxyProps.containerPort,
        //     taskDefinition: mcpProxyDefinition.taskDefinition,
        //     minCapacity: props.mcpProxyProps.minCapacity,
        //     desiredCount: props.mcpProxyProps.desiredTaskCount,
        //     maxCapacity: props.mcpProxyProps.maxCapacity,
        //     capacityProviderStrategies: props.mcpProxyProps.capacityProviderStrategies,
        //     targetCpuUtilizationPercent: props.mcpProxyProps.targetCpuUtilizationPercent,
        //     targetMemoryUtilizationPercent: props.mcpProxyProps.targetMemoryUtilizationPercent,
        //     allowRedisConnection: false,
        //     albs: [
        //         {
        //             id: "int",
        //             priority: props.mcpProxyProps.albRulePriority,
        //             conditions: [
        //                 elbv2.ListenerCondition.pathPatterns(["/mcp/*"]),
        //             ],
        //             healthCheckPath: "/health",
        //         },
        //     ],
        // });

        // TODO: Write LLM Gateway configuration to Parameter Store
        // Get the internal ALB DNS name for the LLM Gateway
    }
}
