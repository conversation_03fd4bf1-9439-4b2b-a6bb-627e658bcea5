import {
	StackProps,
	Stack,
	RemovalPolicy,
	aws_s3 as s3,
	aws_ssm as ssm,
	aws_kms as kms,
} from "aws-cdk-lib";
import * as aca from "aca-cdk-constructs";

/**
 * stack properties
 */
export interface PersistentStackProps extends StackProps {
	readonly appId: string;
	readonly bucketName: string;
	readonly removalPolicy?: RemovalPolicy;
}

/**
 * deploy s3 bucket in its own stack
 */
export class PersistentStack extends Stack {
	constructor(scope: aca.EnvironmentalApp, id: string, props: PersistentStackProps) {
		super(scope, id, props);

		const removalPolicy = props.removalPolicy ?? RemovalPolicy.DESTROY;
		const envName = scope.envName;
		const appId = props.appId;
		const s3KeyArn = ssm.StringParameter.fromStringParameterName(
			this,
			"S3KmsKeyArn",
			aca.SSMParamBuilder.startPath(aca.SSM_ROOT_ALPHA_APP)
				.addPath(envName)
				.addPath("app-s3-kms")
				.addPath("kms-arn")
				.path()
		).stringValue;

		const s3EncryptionKey = kms.Key.fromKeyArn(this, "S3EncryptionKeyLookup", s3KeyArn);

		const bucketProps: s3.BucketProps = {
			bucketName: props.bucketName,
			versioned: false, // no need to version given that this is transient bucket - for pipeline use
			encryptionKey: s3EncryptionKey,
			removalPolicy,
		};

		const bucket = new s3.Bucket(this, "Bucket", bucketProps);

		new aca.SSMWriter(this, "S3BucketNameSSM", {
			parameterName: aca.SSMParamBuilder.startPath(aca.SSM_ROOT_ALPHA)
				.addPath(envName)
				.addPath(appId)
				.addPath("s3")
				.addPath("BucketName")
				.path(),
			parameterValue: bucket.bucketName,
		});

		new aca.SSMWriter(this, "S3BucketArnNameSSM", {
			parameterName: aca.SSMParamBuilder.startPath(aca.SSM_ROOT_ALPHA)
				.addPath(envName)
				.addPath(appId)
				.addPath("s3")
				.addPath("BucketArn")
				.path(),
			parameterValue: bucket.bucketArn,
		});
	}
}
