import {
    EnvironmentalApp,
    createDefaultAlphaEcsApiSettings
} from "aca-cdk-constructs";
import { AppStack } from "../lib/app";
import { PolicyStatement } from "aws-cdk-lib/aws-iam";
import { PersistentStack } from "../lib/persistent";

const app = new EnvironmentalApp({
    appName: "encore",
    tags: {
        application: "encore",
        repository: "encore-api",
    }
});

const appProps = {
    appId: "encore",
    taskRolePolicies: [] as PolicyStatement[],
    kmsKeyAliases: [] as string[]
};

const apiProps = {
    basePath: "/api/encore",
    ecrName: "alpha/encore-api",
    albRulePriority: 88,
    containerPort: 8080,
    cpu: 2048,
    memoryLimitMiB: 4096,
    capacityProviderStrategies: [
        {
            capacityProvider: "FARGATE_SPOT",
            weight: 2,
        },
        {
            capacityProvider: "FARGATE",
            weight: 1,
        },
    ],
};

const sqsProps = {
    taskQueueIdentifier: "encore-task-processor",
};

const taskProcessorProps = {
    ecrName: "alpha/encore-batch",
    containerPort: 8888,
    cpu: 2048,
    memoryLimitMiB: 4096,
    minCapacity: 1,
    desiredTaskCount: 1,
    maxCapacity: 1,
    capacityProviderStrategies: [
        {
            capacityProvider: "FARGATE_SPOT",
            weight: 2,
        },
        {
            capacityProvider: "FARGATE",
            weight: 1,
        },
    ],
    targetCpuUtilizationPercent: 75,
    targetMemoryUtilizationPercent: 75,
};

const llmGatewayProps = {
    basePath: "/api/llm_gateway/v1",
    ecrName: "alpha/encore-llm-gateway",
    containerPort: 8008,
    cpu: 1024,
    memoryLimitMiB: 2048,
    minCapacity: 1,
    desiredTaskCount: 1,
    maxCapacity: 3,
    capacityProviderStrategies: [
        {
            capacityProvider: "FARGATE_SPOT",
            weight: 2,
        },
        {
            capacityProvider: "FARGATE",
            weight: 1,
        },
    ],
    targetCpuUtilizationPercent: 70,
    targetMemoryUtilizationPercent: 70,
    albRulePriority: 89,
};

// TODO: Resolve MCP Proxy Service deployment

// const mcpProxyProps = {
//     ecrName: "alpha/encore-mcp",
//     containerPort: 8080,
//     cpu: 1024,
//     memoryLimitMiB: 2048,
//     minCapacity: 1,
//     desiredTaskCount: 1,
//     maxCapacity: 3,
//     capacityProviderStrategies: [
//         {
//             capacityProvider: "FARGATE_SPOT",
//             weight: 2,
//         },
//         {
//             capacityProvider: "FARGATE",
//             weight: 1,
//         },
//     ],
//     targetCpuUtilizationPercent: 70,
//     targetMemoryUtilizationPercent: 70,
//     albRulePriority: 90,
// };

const kmsKeyAliases = [
    "encore-app-app-s3",
    "encore-app-app-user-data-key", // alphaUserDataKms
    `encore-data-${app.envName}-s3-kms` // alphaShortTermS3Kms
];

const apiEnvSettings = createDefaultAlphaEcsApiSettings(app.envName);
const acaAiBucket = `alpha-${app.envName}-encore-${app.appRegion}`;
const s3BucketNames = [acaAiBucket];

appProps.kmsKeyAliases = kmsKeyAliases;
appProps.taskRolePolicies.push(
    new PolicyStatement({
        actions: [
            "s3:ListBucket",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:DeleteObjectVersion",
            "s3:GetObject",
        ],
        resources: [
            ...s3BucketNames.map((i) => `arn:aws:s3:::${i}`),
            ...s3BucketNames.map((i) => `arn:aws:s3:::${i}/*`),
        ],
    })
);

const persistentStack = new PersistentStack(app, `${app.namingPrefix}-${app.appRegion}-persistance`, {
    appId: appProps.appId,
    bucketName: acaAiBucket,
});

const appStack = new AppStack(app, `${app.namingPrefix}-${app.appRegion}`, {
    ...appProps,
    apiProps: {
        ...apiEnvSettings,
        ...apiProps,
    },
    sqsProps: {
        ...sqsProps,
    },
    taskProcessorProps: {
        ...taskProcessorProps,
    },
    llmGatewayProps: {
        ...llmGatewayProps,
    },
    // TODO: Resolve MCP Proxy Service deployment
    // mcpProxyProps: {
    //     ...mcpProxyProps,
    // },
    env: {
        account: app.envDetails.accountId,
        region: app.appRegion,
    },
    eventBusFilterPolicy: {},
    kmsKeyAliases: kmsKeyAliases,
});
appStack.addDependency(persistentStack);
