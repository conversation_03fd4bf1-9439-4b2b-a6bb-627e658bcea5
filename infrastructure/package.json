{"name": "infrastructure", "version": "0.1.0", "bin": {"infrastructure": "bin/build.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "cdk": "cdk"}, "devDependencies": {"@types/aws-lambda": "^8.10.147", "@types/jest": "^29.4.0", "@types/node": "18.14.6", "@types/pg": "^8.11.10", "aws-cdk": "^2.1018.1", "aws-sdk": "^2.1692.0", "esbuild": "^0.24.2", "ts-node": "^10.9.1", "typescript": "~4.9.5"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.13.0", "@aws-lambda-powertools/parameters": "^2.13.0", "@aws-sdk/client-secrets-manager": "^3.731.1", "aca-cdk-constructs": "^1.102.8", "aws-cdk-lib": "^2.174.1", "constructs": "^10.0.5", "pg": "^8.13.1", "source-map-support": "^0.5.21", "ts-md5": "^1.3.1"}}