#!/bin/bash

set -ex

LITELLM_DIR="/usr/lib/python3.13/site-packages/litellm"
if [ -z "$LITELLM_DIR" ]; then
    echo "Error: litellm installation directory not found."
    exit 1
fi

# Patch only if not already patched
if [ -f "$LITELLM_DIR/is_patched" ]; then
    echo "Litellm is already patched. Skipping patching."
else
    python /tmp/patch_litellm.py "$LITELLM_DIR"
    if [ $? -eq 0 ]; then
        echo "All patches applied successfully!"
        # write is_patched file to indicate that patches have been applied
        echo "true" > $LITELLM_DIR/is_patched
    else
        echo "Warning: Some patches may have failed"
    fi
fi

# Choose config file based on is_local environment variable
if [ "$is_local" = "true" ]; then
    echo "Using local configuration for development"
    CONFIG_FILE="/app/config.local.yaml"
else
    echo "Using production configuration"
    CONFIG_FILE="/app/config.yaml"
fi

litellm --port 8008 --config $CONFIG_FILE --debug
