"""
This is a file for the AWS Parameter Store Integration.
"""

import os
from typing import Any, Optional

from litellm._logging import print_verbose


class AWSParameterStore:
    def __init__(self) -> None:
        self.validate_environment()
        self.ssm_client = self.load_ssm_client()

    def validate_environment(
        self,
    ):
        if "AWS_REGION" not in os.environ:
            raise ValueError("Missing required environment variable - AWS_REGION")

    def load_ssm_client(self):
        try:
            import boto3

            # Create a SSM client
            ssm_client = boto3.client("ssm", region_name=os.getenv("AWS_REGION"))
            return ssm_client
        except ImportError:
            raise ImportError("boto3 is not installed. Please install it with 'pip install boto3'")
        except Exception as e:
            raise e

    def get_secret(self, secret_name: str) -> Optional[Any]:
        if self.ssm_client is None:
            raise ValueError("ssm_client is None")

        # remove prefix if it exists
        secret_name = secret_name.replace("aws.parameter_store", "")

        try:
            response = self.ssm_client.get_parameter(
                Name=secret_name, WithDecryption=True
            )
            return response["Parameter"]["Value"]
        except Exception as e:
            print_verbose(
                f"Error getting secret {secret_name} from AWS Parameter Store: {e}"
            )
            # raise e # for now, just return None, as we might be checking for a secret that doesn't exist. The higher level function should handle this.
            return None
