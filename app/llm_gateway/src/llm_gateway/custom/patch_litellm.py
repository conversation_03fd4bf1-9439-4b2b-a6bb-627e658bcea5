"""# Patch LiteLLM to support Hashicorp Vault and AWS Parameter Store."""

import logging
import os
import shutil
import sys
from enum import Enum

logger = logging.getLogger(__name__)


class PatchType(Enum):
    """Enum to define patch types for better organization and readability."""

    COPY_FILE = "copy_file"
    INJECT_INTO_ENUM = "inject_into_enum"
    REPLACE_LINE = "replace_line"
    INJECT_IMPORT = "inject_import"
    INJECT_AFTER_LINE = "inject_after_line"


PATCHES = [
    (PatchType.COPY_FILE, lambda patcher: patcher.copy_file("aws_parameter_store.py", "secret_managers/aws_parameter_store.py")),
    (
        PatchType.INJECT_INTO_ENUM,
        lambda patcher: patcher.inject_into_enum("litellm/types/secret_managers/main.py", "KeyManagementSystem", "patch_proxy_types.py"),
    ),
    (
        PatchType.REPLACE_LINE,
        lambda patcher: patcher.replace_line_content(
            "proxy/proxy_server.py", 'and value.startswith("os.environ/"):', "patch_proxy_server_1.py"
        ),
    ),
    (
        PatchType.INJECT_AFTER_LINE,
        lambda patcher: patcher.inject_after_line("proxy/proxy_server.py", "HashicorpSecretManager()", "patch_proxy_server_2.py"),
    ),
    (PatchType.INJECT_IMPORT, lambda patcher: patcher.inject_import("secret_managers/main.py", "patch_secret_managers_main_1.py")),
    (
        PatchType.INJECT_AFTER_LINE,
        lambda patcher: patcher.inject_after_line(
            "secret_managers/main.py", 'secret_name = secret_name.replace("os.environ/", "")', "patch_secret_managers_main_2.py"
        ),
    ),
]


class LiteLLMPatcher:
    """Class to handle patching of LiteLLM files."""

    def __init__(self, litellm_dir: str, patches_dir: str = "/tmp/patches"):
        """Initialize the patcher with directories."""
        if not os.path.exists(litellm_dir):
            raise FileNotFoundError(f"Litellm directory does not exist: {litellm_dir}")
        self.litellm_dir = litellm_dir
        self.patches_dir = patches_dir

    def find_file(self, relative_path: str) -> str:
        """Find file in litellm directory"""
        full_path = os.path.join(self.litellm_dir, relative_path)
        if os.path.exists(full_path):
            return full_path

        # Search recursively if not found
        for root, _, files in os.walk(self.litellm_dir):
            for file in files:
                if file == os.path.basename(relative_path):
                    candidate = os.path.join(root, file)
                    # Check if the path structure matches
                    if relative_path.replace("/", os.sep) in candidate:
                        return candidate

        raise FileNotFoundError("Could not find {%s} in {%s}" % (relative_path, self.litellm_dir))

    def read_patch_file(self, patch_file: str) -> str:
        """Read content from patch file"""
        patch_path = os.path.join(self.patches_dir, patch_file)
        with open(patch_path, "r", encoding="utf-8") as f:
            return f.read().strip()

    def copy_file(self, source_file: str, target_relative_path: str):
        """Copy a file to litellm directory"""
        source_path = os.path.join(self.patches_dir, source_file)
        target_path = os.path.join(self.litellm_dir, target_relative_path)

        # Create target directory if it doesn't exist
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        shutil.copy2(source_path, target_path)
        logger.info("Copied {%s} to {%s}", source_file, target_relative_path)

    def inject_into_enum(self, target_file: str, enum_class_name: str, patch_file: str):
        """Inject a line into an enum class"""
        target_path = self.find_file(target_file)
        new_line = self.read_patch_file(patch_file)

        with open(target_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # check if not exists in enum already
        if any(new_line.strip() in line for line in lines):
            logger.warning("Line '{%s}' already exists in {%s} in {%s}", new_line.strip(), enum_class_name, target_file)
            return

        # Find the enum class
        in_enum = False
        enum_end_index = None

        for line_number, line in enumerate(lines):
            if f"class {enum_class_name}" in line and "enum.Enum" in line:
                in_enum = True
                continue

            if in_enum:
                # Look for the end of the enum (next class/function or dedent)
                if line.strip() and not line.startswith("    ") and not line.strip().startswith("#") and not line.strip().startswith('"""'):
                    enum_end_index = line_number
                    break

        if enum_end_index is None:
            # If we didn't find the end, add before the last line of the file
            enum_end_index = len(lines)

        # Insert the new line before the enum ends
        lines.insert(enum_end_index - 1, f"    {new_line}\n")

        with open(target_path, "w", encoding="utf-8") as f:
            f.writelines(lines)

        logger.info("Injected '{%s}' into {%s} in {%s}", new_line, enum_class_name, target_file)

    def replace_line_content(self, target_file: str, search_text: str, patch_file: str):
        """Replace part of a line with new content"""
        target_path = self.find_file(target_file)
        replacement = self.read_patch_file(patch_file)

        with open(target_path, "r", encoding="utf-8") as f:
            content = f.read()

        if search_text not in content:
            logger.warning("Warning: Search text '{%s}' not found in {%s}", search_text, target_file)
            return

        # Extract the replacement pattern from the patch file
        # Expected format: "OLD_TEXT -> NEW_TEXT" or just "NEW_TEXT"
        if " -> " in replacement:
            old_text, new_text = replacement.split(" -> ", 1)
            modified_content = content.replace(old_text.strip(), new_text.strip())
        else:
            # If no arrow, assume we're replacing the search_text with the replacement
            modified_content = content.replace(search_text, replacement)

        with open(target_path, "w", encoding="utf-8") as f:
            f.write(modified_content)

        logger.info("Replaced content in {%s}", target_file)

    def inject_import(self, target_file: str, patch_file: str):
        """Inject an import statement"""
        target_path = self.find_file(target_file)
        import_line = self.read_patch_file(patch_file)

        with open(target_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # Check if import already exists
        if any(import_line.strip() in line for line in lines):
            logger.warning("Import already exists in {%s}", target_file)
            return

        # Find the best place to insert the import
        insert_index = 0
        for line_number, line in enumerate(lines):
            if line.strip().startswith("from litellm.") or line.strip().startswith("import "):
                insert_index = line_number + 1
            elif line.strip() and not line.strip().startswith("#"):
                break

        lines.insert(insert_index, f"{import_line}\n")

        with open(target_path, "w", encoding="utf-8") as f:
            f.writelines(lines)

        logger.info("Added import: {%s}", import_line)

    def inject_after_line(self, target_file: str, search_line: str, patch_file: str):
        """Inject code after a specific line using exact indentation from patch file"""
        target_path = self.find_file(target_file)
        code_to_inject = self.read_patch_file(patch_file)

        with open(target_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # Find the line to insert after
        insert_index = None
        for line_number, line in enumerate(lines):
            if search_line.strip() in line.strip():
                insert_index = line_number + 1
                break

        if insert_index is None:
            logger.warning("Warning: Could not find line '{%s}' in {%s}", search_line, target_file)
            return

        # Split the code to inject into lines and insert exactly as provided
        inject_lines = code_to_inject.split("\n")

        # Remove empty lines at the end only
        while inject_lines and not inject_lines[-1].strip():
            inject_lines.pop()

        # Insert lines exactly as they are in the patch file
        for line_number, inject_line in enumerate(inject_lines):
            if not inject_line.endswith("\n"):
                inject_line += "\n"
            lines.insert(insert_index + line_number, inject_line)

        with open(target_path, "w", encoding="utf-8") as f:
            f.writelines(lines)

        logger.info("Injected {%s} lines after '{%s}' in {%s}", len(inject_lines), search_line, target_file)


def apply_patches(litellm_dir: str, patches_dir: str = "/tmp/patches"):
    """Apply all patches according to the patch configuration"""
    patcher = LiteLLMPatcher(litellm_dir, patches_dir)

    for patch_type, patch_func in PATCHES:
        try:
            patch_func(patcher)
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error("Error applying patch {%s}: %s", patch_type.value, e)
            continue


if __name__ == "__main__":
    if len(sys.argv) != 2:
        logger.info("Usage: python patch_litellm.py <litellm_directory>")
        sys.exit(1)

    apply_patches(sys.argv[1])
