environment_variables: {}

model_list:
  # Azure models
  - model_name: azure:*
    litellm_params:
      model: azure/*
      api_base: os.environ/AZURE_BASE_URL
      api_key: os.environ/AZURE_API_KEY
      api_version: os.environ/AZURE_API_VERSION
    model_info:
      access_groups: ["default-models"]

  # OpenAI models
  - model_name: "openai:*"
    litellm_params:
      model: openai/*
      api_base: https://api.openai.com/v1
      api_key: os.environ/OPENAI_API_KEY
    model_info:
      access_groups: ["default-models"]

general_settings:
  # For local development, we don't use AWS Parameter Store
  # key_management_system: "aws_parameter_store"

  maximum_spend_logs_retention_period: 90d
  maximum_spend_logs_retention_interval: 1d

  disable_spend_logs: false
  disable_master_key_return: true
  disable_retry_on_max_parallel_request_limit_error: false
  disable_reset_budget: true
  disable_adding_master_key_hash_to_db: false
  enable_jwt_auth: false
  enforce_user_param: false

  # Use environment variables for local development
  master_key: os.environ/LITELLM_MASTER_KEY

  # Database Settings - use environment variable
  database_url: os.environ/DATABASE_URL
  database_connection_pool_limit: 100
  database_connection_timeout: 60
  allow_requests_on_db_unavailable: false

  max_parallel_requests: 10
  global_max_parallel_requests: 10
  infer_model_from_keys: true
  background_health_checks: true
  health_check_interval: 300
  alerting: []
  alerting_threshold: 0

litellm_settings:
  num_retries: 2
  request_timeout: 45
  allowed_fails: 3
  cooldown_time: 30

  turn_off_message_logging: false
  redact_user_api_key_info: true
  set_verbose: false
  json_logs: true

  force_ipv4: false

  # Caching settings for local development
  cache: True
  cache_params:
    type: local
    mode: default_on
    ttl: 600

  default_team_settings:
    - team_id: Encore
    - team_id: MarketingReview
    - team_id: eCommunications
    - team_id: ComplianceManagement
    - team_id: MarketAbuse
    - team_id: Platform
