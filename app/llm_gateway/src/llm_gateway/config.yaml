environment_variables: {}

model_list:
  # Azure models
  # - model_name: azure:*
  #   litellm_params:
  #     model: azure/*
  #     api_base: "aws.parameter_store/alpha/dev/encore/llm/AzureBaseUrl" #"https://censored.openai.azure.com/"
  #     api_key: "aws.parameter_store/alpha/dev/encore/llm/AzureApiKey"
  #     api_version: "aws.parameter_store/alpha/dev/encore/llm/AzureApiVersion"
  #   model_info:
  #     access_groups: ["default-models"]
  # - model_name: phi-4
  #   litellm_params:
  #     model: azure_ai/phi-4
  #     api_base: os.environ/AZURE_STUDIO_API_BASE #"https://censored.services.ai.azure.com/models"
  #     api_key: "aws.parameter_store/alpha/dev/encore/llm/AzureApiKey"
  #     api_version: "aws.parameter_store/alpha/dev/encore/llm/AzureApiVersion" # BUG: NOT SENT IN REQUEST !

  # OpenAI models
  # - model_name: "openai:*"
  #   litellm_params:
  #     model: openai/*
  #     api_base: https://api.openai.com/v1
  #     api_key: aws.parameter_store/alpha/dev/encore/llm/OpenAiApiKey
  #   model_info:
  #     access_groups: ["default-models"]

  # Bedrock models
  - model_name: "bedrock-claude-3-5-sonnet"
    litellm_params:
      model: "bedrock/anthropic.claude-3-5-sonnet-20240620-v1:0"
      aws_region_name: $AWS_REGION
    model_info:
      access_groups: ["default-models"]
  - model_name: aws:sagemaker-completion-model
    litellm_params:
      model: sagemaker/berri-benchmarking-Llama-2-70b-chat-hf-4
      aws_region_name: $AWS_REGION
      input_cost_per_second: 0.000420

  # embedding models
  # - model_name: aws:titan-embed-*
  #   litellm_params:
  #     model: "bedrock/amazon.titan-embed-*"
  #     aws_region_name: $AWS_REGION
  #   model_info:
  #     access_groups: ["embedding-models"]

  # - model_name: azure:embeddings-*
  #   litellm_params:
  #     model: "azure/azure-embedding-*"
  #     api_base: "aws.parameter_store/alpha/dev/encore/llm/AzureBaseUrl"
  #     api_key: "aws.parameter_store/alpha/dev/encore/llm/AzureApiKey"
  #     api_version: "2023-07-01-preview"
  #   model_info:
  #     access_groups: ["embedding-models"]

#router_settings:
#  routing_strategy: latency-based-routing or usage-based-routing-v2
#  enable_pre_call_check: true

# guardrails:
#   - guardrail_name: general-guard
#     litellm_params:
#       guardrail: aim
#       mode: [pre_call, post_call]
#       api_key: os.environ/AIM_API_KEY
#       api_base: os.environ/AIM_API_BASE
#       default_on: true # Optional

#   - guardrail_name: "aporia-pre-guard"
#     litellm_params:
#       guardrail: aporia  # supported values: "aporia", "lakera"
#       mode: "during_call"
#       api_key: os.environ/APORIA_API_KEY_1
#       api_base: os.environ/APORIA_API_BASE_1
#   - guardrail_name: "aporia-post-guard"
#     litellm_params:
#       guardrail: aporia  # supported values: "aporia", "lakera"
#       mode: "post_call"
#       api_key: os.environ/APORIA_API_KEY_2
#       api_base: os.environ/APORIA_API_BASE_2
#     guardrail_info: # Optional field, info is returned on GET /guardrails/list
#       # you can enter any fields under info for consumers of your guardrail
#       params:
#         - name: "toxicity_score"
#           type: "float"
#           description: "Score between 0-1 indicating content toxicity level"
#         - name: "pii_detection"
#           type: "boolean"

general_settings:
  key_management_system: "aws_parameter_store"
  key_management_settings:
    region: "us-east-1"
    # parameter_prefix: "/alpha/dev/encore/llm/"
    cache_ttl: 300 # Cache parameters for 5 minutes
    decrypt_secure_strings: true
    max_retries: 3
    timeout: 30
    store_virtual_keys: false # OPTIONAL. Defaults to False, when True will store virtual keys in secret manager
    # prefix_for_stored_virtual_keys: "/alpha/dev/encore/llm_gateway/" # OPTIONAL. If set, this prefix will be used for stored virtual keys in the secret manager
    access_mode: "read_and_write" # Literal["read_only", "write_only", "read_and_write"]
    hosted_keys: []
    # primary_secret_name: "litellm_secrets" # 👈 Read multiple keys from one JSON secret

  parameter_store_settings:
    region: "us-east-1"
    timeout: 30
    max_retries: 3

  maximum_spend_logs_retention_period: 90d # The maximum time to retain spend logs before deletion.
  maximum_spend_logs_retention_interval: 1d # interval in which the spend log cleanup task should run in.

  #completion_model: string
  disable_spend_logs: false # turn off writing each transaction to the db
  disable_master_key_return: true # turn off returning master key on UI (checked on '/user/info' endpoint)
  disable_retry_on_max_parallel_request_limit_error: false # turn off retries when max parallel request limit is reached
  disable_reset_budget: true # turn off reset budget scheduled task
  disable_adding_master_key_hash_to_db: false # turn off storing master key hash in db, for spend tracking
  enable_jwt_auth: false # allow proxy admin to auth in via jwt tokens with 'litellm_proxy_admin' in claims
  enforce_user_param: false # requires all openai endpoint requests to have a 'user' param
  #allowed_routes: ["route1", "route2"]  # list of allowed proxy API routes - a user can access. (currently JWT-Auth only)
  master_key: aws.parameter_store/alpha/dev/encore/llm/LitellmMasterKey

  # Database Settings
  database_url: aws.parameter_store/alpha/dev/encore/database/ConnectionString
  database_connection_pool_limit: 100 # default 100
  database_connection_timeout: 60 # default 60s
  allow_requests_on_db_unavailable: false # if true, will allow requests that can not connect to the DB to verify Virtual Key to still work

  #custom_auth: string
  max_parallel_requests: 10 # the max parallel requests allowed per deployment
  global_max_parallel_requests: 10 # the max parallel requests allowed on the proxy all up
  infer_model_from_keys: true
  background_health_checks: true
  health_check_interval: 300
  alerting: [] # ["slack", "email"]
  alerting_threshold: 0
  use_client_credentials_pass_through_routes: boolean # use client credentials for all pass through routes like "/vertex-ai", /bedrock/. When this is True Virtual Key auth will not be applied on these endpoints

# TODO: Put back OTEL OLTP settings for crewai once dependency conflicts are resolved
# callback_settings:
#   otel:
#     message_logging: true  # OTEL logging callback specific settings

litellm_settings:
  num_retries: 2 # retry call 2 times on each model_name (e.g. llama-3.1-8b)
  request_timeout: 45 # (int) llm requesttimeout in seconds. Raise Timeout error if call takes longer than 10s. Sets litellm.request_timeout
  allowed_fails: 3 # cooldown model if it fails > 1 call in a minute.
  cooldown_time: 30 # how long to cooldown model if fails/min > allowed_fails

  # Logging/Callback settings
  #success_callback: ["langfuse"]  # list of success callbacks
  #failure_callback: ["sentry"]  # list of failure callbacks
  #callbacks: ["otel"]  # list of callbacks - runs on success and failure
  #service_callbacks: ["datadog", "prometheus"]  # logs redis, postgres failures on datadog, prometheus
  turn_off_message_logging: false # prevent the messages and responses from being logged to on your callbacks, but request metadata will still be logged.
  redact_user_api_key_info: true # Redact information about the user api key (hashed token, user_id, team id, etc.), from logs. Currently supported for Langfuse, OpenTelemetry, Logfire, ArizeAI logging.
  set_verbose: false # sets litellm.set_verbose=True to view verbose debug logs. DO NOT LEAVE THIS ON IN PRODUCTION
  json_logs: true # if true, logs will be in json format

  # Networking settings
  force_ipv4: false # If true, litellm will force ipv4 for all LLM requests. Some users have seen httpx ConnectionError when using ipv6 + Anthropic API

  # TODO: low priority but we may consider
  # Fallbacks, reliability
  #default_fallbacks: ["claude-opus"] # set default_fallbacks, in case a specific model group is misconfigured / bad.
  #content_policy_fallbacks: [{"gpt-3.5-turbo-small": ["claude-opus"]}] # fallbacks for ContentPolicyErrors
  #context_window_fallbacks: [{"gpt-3.5-turbo-small": ["gpt-3.5-turbo-large", "claude-opus"]}] # fallbacks for ContextWindowExceededErrors

  # Caching settings
  cache: True # TODO
  cache_params: # set cache params for redis
    type: local # type of cache to initialize

    # Optional - Supported call types for caching
    #supported_call_types: ["acompletion", "atext_completion", "aembedding", "atranscription"]
    # /chat/completions, /completions, /embeddings, /audio/transcriptions
    mode: default_on # if default_off, you need to opt in to caching on a per call basis
    ttl: 600 # ttl for caching

    # Optional - Redis Settings
    host: "localhost" # The host address for the Redis cache. Required if type is "redis".
    port: 6379 # The port number for the Redis cache. Required if type is "redis".
    password: "your_password" # The password for the Redis cache. Required if type is "redis".
    namespace: "litellm.caching.caching" # namespace for redis cache

    # Optional - Redis Cluster Settings
    redis_startup_nodes: [{ "host": "127.0.0.1", "port": "7001" }]

  default_team_settings:
    - team_id: Encore
    - team_id: MarketingReview
    - team_id: eCommunications
    - team_id: ComplianceManagement
    - team_id: MarketAbuse
    - team_id: Platform
