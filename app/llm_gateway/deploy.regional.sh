#!/bin/bash
set -eu

echo "app deploy.regional.sh script is running"

DEPLOY_MODULE="#{Octopus.ProjectGroup.Name}"
DEPLOY_PROJECT="#{Octopus.Project.Name}"
DEPLOY_REGION="#{AwsRegion}"
DEPLOY_ENVIRONMENT="#{StandardizedEnvironment}"
DEPLOY_ECS_CLUSTER_NAME="#{AlphaAwsEcsClusterName}"
DEPLOY_ECS_SERVICE_NAME="encore-llm-gateway"

process_ids=()
appRoot="/alpha/$DEPLOY_ENVIRONMENT/encore"

. ./scripts/ssm/set-standard-parameter.sh "$appRoot/llm/OpenAiOrganization" "#{OpenAiOrganization}" & process_ids+=($!)
. ./scripts/ssm/set-standard-parameter.sh "$appRoot/llm/OpenAiProject" "#{OpenAiProject}" & process_ids+=($!)
. ./scripts/ssm/set-secure-parameter.sh "$appRoot/llm/OpenAiApiKey" "#{OpenAiApiKey}" & process_ids+=($!)

. ./scripts/ssm/set-standard-parameter.sh "$appRoot/llm/AzureBaseUrl" "#{AzureBaseUrl}" & process_ids+=($!)
. ./scripts/ssm/set-standard-parameter.sh "$appRoot/llm/AzureApiVersion" "#{AzureApiVersion}" & process_ids+=($!)
. ./scripts/ssm/set-secure-parameter.sh "$appRoot/llm/AzureApiKey" "#{AzureApiKey}" & process_ids+=($!)

. ./scripts/ssm/set-secure-parameter.sh "$appRoot/llm/LlmGatewayBaseUrl" '#{LlmGatewayBaseUrl}' & process_ids+=($!)
. ./scripts/ssm/set-secure-parameter.sh "$appRoot/llm/LitellmMasterKey" '#{LitellmMasterKey}' & process_ids+=($!)

. ./scripts/utilities/wait-for-processes.sh "${process_ids[@]}"

. ./scripts/ecs/force-deployment-and-wait.sh
