#!/bin/bash
set -eu

DEPLOY_REGIONS=(#{DeploymentRegions})
DEPLOY_ECR_NAME="alpha/encore-llm-gateway"
DEPLOY_ECR_PRIMARY_TAG="#{Octopus.Release.Number}"
DEPLOY_ENVIRONMENT="#{StandardizedEnvironment}"
DEPLOY_ECR_BUILD_PATH="./"

# Start of cross-region-build-image.sh content
if [[ -z "$DEPLOY_REGIONS" ]]; then
    echo "DEPLOY_REGIONS is required"
    exit 1
fi
if [[ -z "$DEPLOY_ECR_NAME" ]]; then
    echo "DEPLOY_ECR_NAME is required"
    exit 1
fi
if [[ -z "$DEPLOY_ECR_PRIMARY_TAG" ]]; then
    echo "DEPLOY_ECR_PRIMARY_TAG is required"
    exit 1
fi
if [[ -z "$DEPLOY_ECR_BUILD_PATH" ]]; then
    echo "DEPLOY_ECR_BUILD_PATH is required"
    exit 1
fi

echo "Checking if image exists"
IMAGE_META="$( aws ecr list-images --region ${DEPLOY_REGIONS[0]} --repository-name $DEPLOY_ECR_NAME --query "imageIds[?imageTag=='$DEPLOY_ECR_PRIMARY_TAG'].imageTag" --output text )"

errorCode=$?
if [[ $errorCode != 0 ]]; then
    echo "Failed to connect to ECR"
    exit $errorCode
elif [ -z "$IMAGE_META" ]; then
    echo "Publishing image"

    docker -v
    docker builder prune -f

    echo "Building image with Environment=$DEPLOY_ENVIRONMENT"
    (cd $DEPLOY_ECR_BUILD_PATH && docker build --progress=plain -t app:$DEPLOY_ECR_PRIMARY_TAG . --build-arg Environment=$DEPLOY_ENVIRONMENT 2>&1)


    for region in "${DEPLOY_REGIONS[@]}"; do
        ecrHost="454115844779.dkr.ecr.$region.amazonaws.com"

        docker tag app:$DEPLOY_ECR_PRIMARY_TAG $ecrHost/$DEPLOY_ECR_NAME:$DEPLOY_ECR_PRIMARY_TAG
        docker push $ecrHost/$DEPLOY_ECR_NAME:$DEPLOY_ECR_PRIMARY_TAG
        docker image rm $ecrHost/$DEPLOY_ECR_NAME:$DEPLOY_ECR_PRIMARY_TAG
    done

    docker image rm app:$DEPLOY_ECR_PRIMARY_TAG

else
    echo "Image has already been published"
fi
# End of cross-region-build-image.sh content

process_ids=()
for region in "${DEPLOY_REGIONS[@]}"; do
    . ./scripts/ecr/add-image-tag.sh "$region" "#{StandardizedEnvironment}" & process_ids+=($!)
done

. ./scripts/utilities/wait-for-processes.sh "${process_ids[@]}"
