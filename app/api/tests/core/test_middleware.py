"""
Unit tests for middleware classes
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from api.core.middleware import ClientIdToLiteLLMTagsMiddleware
from fastapi import Request, Response


class TestClientIdToLiteLLMTagsMiddleware:
    """Test cases for ClientIdToLiteLLMTagsMiddleware"""

    @pytest.fixture
    def middleware(self) -> ClientIdToLiteLLMTagsMiddleware:
        """Create a middleware instance for testing"""
        # Create a dummy app for the middleware constructor
        dummy_app = MagicMock()
        return ClientIdToLiteLLMTagsMiddleware(dummy_app)

    @pytest.fixture
    def mock_request(self) -> MagicMock:
        """Create a mock request with headers"""
        request = MagicMock(spec=Request)
        request.headers = {}
        request.scope = {"headers": []}
        return request

    @pytest.fixture
    def mock_call_next(self) -> AsyncMock:
        """Create a mock call_next function"""
        return AsyncMock(return_value=Response())

    @pytest.mark.asyncio
    async def test_middleware_transforms_x_encore_id_to_x_litellm_tags(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware correctly transforms x-encore-id header to x-litellm-tags"""
        # Arrange
        client_id = "mr"
        mock_request.headers = {"x-encore-id": client_id}
        mock_request.scope = {"headers": [(b"x-encore-id", client_id.encode())]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that the scope headers were updated
        assert mock_request.scope["headers"] == [(b"x-encore-id", client_id.encode()), (b"x-litellm-tags", client_id.encode())]

    @pytest.mark.asyncio
    async def test_middleware_preserves_existing_headers(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware preserves existing headers when adding x-litellm-tags"""
        # Arrange
        client_id = "encore"
        existing_headers = {"x-encore-id": client_id, "content-type": "application/json", "authorization": "Bearer token123"}
        mock_request.headers = existing_headers
        mock_request.scope = {
            "headers": [
                (b"x-encore-id", client_id.encode()),
                (b"content-type", b"application/json"),
                (b"authorization", b"Bearer token123"),
            ]
        }

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that all existing headers are preserved
        scope_headers = mock_request.scope["headers"]
        assert (b"x-encore-id", client_id.encode()) in scope_headers
        assert (b"content-type", b"application/json") in scope_headers
        assert (b"authorization", b"Bearer token123") in scope_headers
        assert (b"x-litellm-tags", client_id.encode()) in scope_headers

    @pytest.mark.asyncio
    async def test_middleware_handles_missing_x_encore_id_header(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware handles requests without x-encore-id header gracefully"""
        # Arrange
        mock_request.headers = {"content-type": "application/json"}
        mock_request.scope = {"headers": [(b"content-type", b"application/json")]}

        # Act
        with patch("api.core.middleware.log") as mock_log:
            await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that no x-litellm-tags header was added
        scope_headers = mock_request.scope["headers"]
        assert not any(header[0] == b"x-litellm-tags" for header in scope_headers)
        # Check that warning was logged
        mock_log.warning.assert_called_once_with("No 'x-encore-id' header found. Skipping 'x-litellm-tags' transformation.")

    @pytest.mark.asyncio
    async def test_middleware_handles_empty_x_encore_id_header(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware handles empty x-encore-id header"""
        # Arrange
        mock_request.headers = {"x-encore-id": ""}
        mock_request.scope = {"headers": [(b"x-encore-id", b"")]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that x-litellm-tags header was added with empty value
        scope_headers = mock_request.scope["headers"]
        assert (b"x-litellm-tags", b"") in scope_headers

    @pytest.mark.asyncio
    async def test_middleware_handles_special_characters_in_client_id(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware handles special characters in client ID"""
        # Arrange
        client_id = "@ecomms"
        mock_request.headers = {"x-encore-id": client_id}
        mock_request.scope = {"headers": [(b"x-encore-id", client_id.encode())]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that special characters were preserved
        scope_headers = mock_request.scope["headers"]
        assert (b"x-litellm-tags", client_id.encode()) in scope_headers

    @pytest.mark.asyncio
    async def test_middleware_overwrites_existing_x_litellm_tags(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware overwrites existing x-litellm-tags header"""
        # Arrange
        client_id = "new-client-id"
        existing_litellm_tags = "old-tags"
        mock_request.headers = {"x-encore-id": client_id, "x-litellm-tags": existing_litellm_tags}
        mock_request.scope = {"headers": [(b"x-encore-id", client_id.encode()), (b"x-litellm-tags", existing_litellm_tags.encode())]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that x-litellm-tags was updated with the new value
        scope_headers = mock_request.scope["headers"]
        assert (b"x-litellm-tags", client_id.encode()) in scope_headers
        # Check that the old value is no longer present
        assert not any(header == (b"x-litellm-tags", existing_litellm_tags.encode()) for header in scope_headers)

    @pytest.mark.asyncio
    async def test_middleware_preserves_case_sensitivity(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware preserves case sensitivity in client ID"""
        # Arrange
        client_id = "EComms"
        mock_request.headers = {"x-encore-id": client_id}
        mock_request.scope = {"headers": [(b"x-encore-id", client_id.encode())]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that case sensitivity was preserved
        scope_headers = mock_request.scope["headers"]
        assert (b"x-litellm-tags", client_id.encode()) in scope_headers

    @pytest.mark.asyncio
    async def test_middleware_returns_response_from_call_next(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware returns the response from call_next"""
        # Arrange
        expected_response = Response(content="test response", status_code=200)
        mock_call_next.return_value = expected_response
        mock_request.headers = {"x-encore-id": "encore"}

        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert response == expected_response
        assert mock_call_next.called

    @pytest.mark.asyncio
    async def test_middleware_handles_call_next_exception(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware properly propagates exceptions from call_next"""
        # Arrange
        test_exception = Exception("Test exception")
        mock_call_next.side_effect = test_exception
        mock_request.headers = {"x-encore-id": "encore"}

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            await middleware.dispatch(mock_request, mock_call_next)

        assert exc_info.value == test_exception

    @pytest.mark.asyncio
    async def test_middleware_handles_malformed_headers(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware handles malformed headers gracefully"""
        # Arrange
        mock_request.headers = None  # Malformed headers
        mock_request.scope = {"headers": None}

        # Act
        with patch("api.core.middleware.log") as mock_log:
            await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Should handle gracefully without crashing
        mock_log.warning.assert_called_once_with("No 'x-encore-id' header found. Skipping 'x-litellm-tags' transformation.")

    @pytest.mark.asyncio
    async def test_middleware_handles_multiple_x_encore_id_headers(
        self, middleware: ClientIdToLiteLLMTagsMiddleware, mock_request: MagicMock, mock_call_next: AsyncMock
    ) -> None:
        """Test that middleware handles multiple x-encore-id headers (takes the first one)"""
        # Arrange
        client_id1 = "encore"
        client_id2 = "ecomms"
        mock_request.headers = {"x-encore-id": [client_id1, client_id2]}
        mock_request.scope = {"headers": [(b"x-encore-id", client_id1.encode()), (b"x-encore-id", client_id2.encode())]}

        # Act
        await middleware.dispatch(mock_request, mock_call_next)

        # Assert
        assert mock_call_next.called
        # Check that the first client ID was used
        scope_headers = mock_request.scope["headers"]
        assert (b"x-litellm-tags", client_id1.encode()) in scope_headers
