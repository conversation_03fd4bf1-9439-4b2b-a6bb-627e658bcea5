import os
import sys
import uuid
from unittest.mock import AsyncMock

import pytest
from common.enums import BatchJobStatusEnum
from common.settings import settings

# pylint: disable=C0401  # Disable import order check
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

settings.llm.default_model = "azure:gpt-4o"


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_text(mocker, fastapi_client):
    """Test text generation endpoint."""
    mock_llm_service_class = mocker.patch("api.routes.llm.LlmService")

    mock_llm_service_instance = AsyncMock()
    mock_llm_service_class.return_value = mock_llm_service_instance
    mock_llm_service_instance.generate.return_value = {"text": "This is a joke about computers."}

    response = fastapi_client.post(
        "/generate",
        json={
            "prompt": "Tell me a joke about computers",
            "context": "You are a funny person",
            "output_type": "text",
        },
    )

    assert response.status_code == 200
    assert mock_llm_service_class.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_json(mocker, fastapi_client):
    mock_llm_service_class = mocker.patch("api.routes.llm.LlmService")

    mock_llm_service_instance = AsyncMock()
    mock_llm_service_class.return_value = mock_llm_service_instance
    mock_llm_service_instance.generate.return_value = {"text": "This is a joke about computers."}

    response = fastapi_client.post(
        "/generate",
        json={
            "prompt": "Steve is two years older than Mark, who is 5 years old.  How old is Steve?  Answer in JSON format with the following schema { "
            "age"
            ": int }.  Only return the raw JSON and nothing else.",
            "output_type": "json",
        },
    )

    assert response.status_code == 200
    assert mock_llm_service_class.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_stream(mocker, fastapi_client):
    mock_llm_service_class = mocker.patch("api.routes.llm.LlmService")

    mock_llm_service_instance = AsyncMock()
    mock_llm_service_class.return_value = mock_llm_service_instance
    mock_llm_service_instance.generate.return_value = {"text": "This is a joke about computers."}

    response = fastapi_client.post(
        "/generate",
        json={
            "prompt": "What is the SEC new marketing rule?",
            "context": "You are a compliance analyst",
            "stream": True,
        },
    )

    assert response.status_code == 200
    assert mock_llm_service_class.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_batch(mocker, fastapi_client, fake_batch_job_id):
    mock_get_batch_job_manager = mocker.patch("api.routes.llm.get_batch_job_manager")
    mock_get_batch_job_manager.return_value.create_job.return_value = fake_batch_job_id
    mock_task_generate_batch = mocker.patch("api.routes.llm.task_generate_batch")
    response = fastapi_client.post(
        "/generate-batch",
        json={
            "location": "s3://bucket-name/path/to/file.json",
            "prompt": "You are a helpful assistant",
        },
    )

    assert response.status_code == 200
    assert mock_get_batch_job_manager.call_count == 1
    assert mock_get_batch_job_manager.return_value.create_job.call_count == 1
    assert mock_task_generate_batch.send.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_batch_with_model(mocker, fastapi_client, fake_batch_job_id):
    mock_get_batch_job_manager = mocker.patch("api.routes.llm.get_batch_job_manager")
    mock_get_batch_job_manager.return_value.create_job.return_value = fake_batch_job_id
    mock_task_generate_batch = mocker.patch("api.routes.llm.task_generate_batch")
    response = fastapi_client.post(
        "/generate-batch",
        json={
            "location": "s3://bucket-name/path/to/file.json",
            "prompt": "You are a helpful assistant",
            "model": "azure:gpt-4o",
        },
    )

    assert response.status_code == 200
    assert mock_get_batch_job_manager.call_count == 1
    assert mock_get_batch_job_manager.return_value.create_job.call_count == 1
    assert mock_task_generate_batch.send.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_generate_batch_missing_prompt(mocker, fastapi_client, fake_batch_job_id):
    mock_get_batch_job_manager = mocker.patch("api.routes.llm.get_batch_job_manager")
    mock_get_batch_job_manager.return_value.create_job.return_value = fake_batch_job_id
    mock_task_generate_batch = mocker.patch("api.routes.llm.task_generate_batch")
    response = fastapi_client.post(
        "/generate-batch",
        json={
            "location": "s3://bucket-name/path/to/file.json",
        },
    )

    assert response.status_code == 422
    assert mock_get_batch_job_manager.call_count == 0
    assert mock_get_batch_job_manager.return_value.create_job.call_count == 0
    assert mock_task_generate_batch.send.call_count == 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_status_invalid_job_id(mocker, fastapi_client):
    mock_get_batch_job_manager = mocker.patch("api.routes.llm.get_batch_job_manager")
    mock_task_generate_batch = mocker.patch("api.routes.llm.task_generate_batch")
    response = fastapi_client.get("/jobs/12345/status")

    assert response.status_code == 400
    assert mock_get_batch_job_manager.call_count == 0
    assert mock_get_batch_job_manager.return_value.create_job.call_count == 0
    assert mock_task_generate_batch.send.call_count == 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_status_processing(mocker, fastapi_client, fake_batch_job_id, fake_batch_job_in_processing_state):
    fake_job_progress = (5, 5, 0, 0, "s3_prefix")  # total, processing, completed, failed
    mock_validate_job_id = mocker.patch("api.routes.llm.validate_job_id", return_value=uuid.UUID(fake_batch_job_id))
    mock_calculate_progress = mocker.patch("api.routes.llm.calculate_progress", return_value=fake_job_progress)
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data", return_value=fake_batch_job_in_processing_state)
    mock_task_check_job = mocker.patch("api.routes.llm.task_check_job")
    response = fastapi_client.get(f"/jobs/{fake_batch_job_id}/status")

    assert response.status_code == 200
    job_response = response.json()
    assert job_response["job_id"] == str(fake_batch_job_id)
    assert job_response["s3_prefix"] == "s3_prefix"
    assert job_response["status"] == BatchJobStatusEnum.PROCESSING.name.lower()
    assert job_response["progress"] == {
        "total": 5,
        "completed": 0,
        "failed": 0,
        "processing": 5,
    }
    assert mock_validate_job_id.call_count == 1
    assert mock_calculate_progress.call_count == 1
    assert mock_fetch_job_data.call_count == 1
    assert mock_task_check_job.send.call_count == 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_status_completed(mocker, fastapi_client, fake_batch_job_id, fake_batch_job_in_completed_state):
    fake_job_progress = (5, 0, 5, 0, "s3_prefix")  # total, processing, completed, failed
    mock_validate_job_id = mocker.patch("api.routes.llm.validate_job_id", return_value=uuid.UUID(fake_batch_job_id))
    mock_calculate_progress = mocker.patch("api.routes.llm.calculate_progress", return_value=fake_job_progress)
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data", return_value=fake_batch_job_in_completed_state)
    mock_task_check_job = mocker.patch("api.routes.llm.task_check_job")
    response = fastapi_client.get(f"/jobs/{fake_batch_job_id}/status")

    assert response.status_code == 200
    job_response = response.json()
    assert job_response["job_id"] == str(fake_batch_job_id)
    assert job_response["s3_prefix"] == "s3_prefix"
    assert job_response["status"] == BatchJobStatusEnum.COMPLETED.name.lower()
    assert job_response["progress"] == {
        "total": 5,
        "completed": 5,
        "failed": 0,
        "processing": 0,
    }
    assert mock_validate_job_id.call_count == 1
    assert mock_calculate_progress.call_count == 1
    assert mock_fetch_job_data.call_count == 1
    assert mock_task_check_job.send.call_count == 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_status_stuck(mocker, fastapi_client, fake_batch_job_id, fake_batch_job_in_processing_state):
    """Test job status when the job is stuck in processing state."""
    fake_job_progress = (5, 0, 5, 0, "s3_prefix")  # total, processing, completed, failed
    mock_validate_job_id = mocker.patch("api.routes.llm.validate_job_id", return_value=uuid.UUID(fake_batch_job_id))
    mock_calculate_progress = mocker.patch("api.routes.llm.calculate_progress", return_value=fake_job_progress)
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data", return_value=fake_batch_job_in_processing_state)
    mock_task_check_job = mocker.patch("api.routes.llm.task_check_job")
    response = fastapi_client.get(f"/jobs/{fake_batch_job_id}/status")

    assert response.status_code == 200
    job_response = response.json()
    assert job_response["job_id"] == str(fake_batch_job_id)
    assert job_response["s3_prefix"] == "s3_prefix"
    assert job_response["status"] == BatchJobStatusEnum.PROCESSING.name.lower()
    assert job_response["progress"] == {
        "total": 5,
        "completed": 5,
        "failed": 0,
        "processing": 0,
    }
    assert mock_validate_job_id.call_count == 1
    assert mock_calculate_progress.call_count == 1
    assert mock_fetch_job_data.call_count == 1
    assert mock_task_check_job.send.call_count == 1


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_results_invalid_job_id(mocker, fastapi_client):
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data")
    response = fastapi_client.get("/jobs/12345/results")

    assert response.status_code == 400
    assert mock_fetch_job_data.call_count == 0


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_results_completed_job(mocker, fastapi_client, fake_batch_job_id, fake_batch_job_in_completed_state):
    mock_validate_job_id = mocker.patch("api.routes.llm.validate_job_id", return_value=uuid.UUID(fake_batch_job_id))
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data", return_value=fake_batch_job_in_completed_state)
    mock_prepare_presigned_urls = mocker.patch(
        "api.routes.llm.prepare_presigned_urls",
        return_value=(["presigned_url1", "presigned_url2"], {"page": 1, "page_size": 10}),
    )
    response = fastapi_client.get(f"/jobs/{fake_batch_job_id}/results")

    assert response.status_code == 200
    assert mock_validate_job_id.call_count == 1
    assert mock_fetch_job_data.call_count == 1
    assert mock_prepare_presigned_urls.call_count == 1

    response_data = response.json()
    assert response_data["job_id"] == str(fake_batch_job_id)
    assert response_data["status"] == BatchJobStatusEnum.COMPLETED.name.lower()
    assert response_data["result_urls"] == ["presigned_url1", "presigned_url2"]
    assert response_data["pagination"] == {"page": 1, "page_size": 10}


@pytest.mark.integration
@pytest.mark.asyncio
async def test_job_results_incomplete_job(mocker, fastapi_client, fake_batch_job_id, fake_batch_job_in_processing_state):
    mock_validate_job_id = mocker.patch("api.routes.llm.validate_job_id", return_value=uuid.UUID(fake_batch_job_id))
    mock_fetch_job_data = mocker.patch("api.routes.llm.fetch_job_data", return_value=fake_batch_job_in_processing_state)
    mock_prepare_presigned_urls = mocker.patch(
        "api.routes.llm.prepare_presigned_urls",
        return_value=(["presigned_url1", "presigned_url2"], {"page": 1, "page_size": 10}),
    )
    response = fastapi_client.get(f"/jobs/{fake_batch_job_id}/results")

    assert response.status_code == 200
    assert mock_validate_job_id.call_count == 1
    assert mock_fetch_job_data.call_count == 1
    assert mock_prepare_presigned_urls.call_count == 0
