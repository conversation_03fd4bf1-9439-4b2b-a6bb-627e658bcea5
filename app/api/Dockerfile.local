FROM python:3.11-slim-bullseye AS base

ENV DEBIAN_FRONTEND=noninteractive

RUN groupadd -r appuser && \
    useradd -r -g appuser -d /home/<USER>
    apt-get update && \
    apt-get install -y --no-install-recommends curl procps && \
    rm -rf /var/lib/apt/lists/*

FROM base AS builder

COPY --from=ghcr.io/astral-sh/uv:0.5.18 /uv /bin/uv
ENV UV_COMPILE_BYTECODE=1 UV_LINK_MODE=copy

WORKDIR /app

ARG PACKAGE=api
COPY pyproject.toml uv.lock /app/

COPY app/api/pyproject.toml /app/app/api/
COPY app/common/pyproject.toml /app/app/common/

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project --no-dev --package "${PACKAGE}"

COPY app/api/src/ /app/
COPY app/api/scripts/ /app/scripts
COPY app/common/src/common /app/common

FROM base
COPY --from=builder --chown=appuser:appuser /app /app

ENV PATH="/app/.venv/bin:$PATH"

ARG Environment
ENV env_name=${Environment}

WORKDIR /app

RUN chown -R appuser:appuser /app
RUN mkdir -p /home/<USER>/.local/share && chown -R appuser:appuser /home/<USER>/.local

USER appuser

EXPOSE 8080

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    LD_LIBRARY_PATH=/usr/local/lib

CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1"]
