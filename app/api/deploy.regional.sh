#!/bin/bash
set -eu

echo "app deploy.regional.sh script is running"

DEPLOY_MODULE="#{Octopus.ProjectGroup.Name}"
DEPLOY_PROJECT="#{Octopus.Project.Name}"
DEPLOY_REGION="#{AwsRegion}"
DEPLOY_ENVIRONMENT="#{StandardizedEnvironment}"
DEPLOY_ECS_CLUSTER_NAME="#{AlphaAwsEcsClusterName}"
DEPLOY_ECS_SERVICE_NAME="encore-api"

process_ids=()
appRoot="/alpha/$DEPLOY_ENVIRONMENT/encore"

. ./scripts/ssm/set-standard-parameter.sh "$appRoot/pipeline/MaxWorkers" "#{MaxWorkers}" & process_ids+=($!)

# LLM
. ./scripts/ssm/set-standard-parameter.sh "$appRoot/llm/DefaultModel" "#{DefaultModel}" & process_ids+=($!)

. ./scripts/ssm/set-secure-parameter.sh "$appRoot/otlp/OTEL_EXPORTER_OTLP_HEADERS" '#{OTEL_EXPORTER_OTLP_HEADERS}' & process_ids+=($!)
. ./scripts/ssm/set-standard-parameter.sh "$appRoot/otlp/OTEL_EXPORTER_OTLP_ENDPOINT" '#{OTEL_EXPORTER_OTLP_ENDPOINT}' & process_ids+=($!)
. ./scripts/ssm/set-standard-parameter.sh "$appRoot/otlp/OTEL_EXPORTER_OTLP_PROTOCOL" '#{OTEL_EXPORTER_OTLP_PROTOCOL}' & process_ids+=($!)

. ./scripts/utilities/wait-for-processes.sh "${process_ids[@]}"

. ./scripts/ecs/force-deployment-and-wait.sh
