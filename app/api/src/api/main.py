"""
Main application entry point for the FastAPI application.
"""

import os
from pathlib import Path

import litellm.proxy.client as litellm
import yaml
from common.logging import LoggingSetup
from common.settings import settings
from common.utils import store_in_secrets_manager
from fastapi import APIRouter, FastAPI

from api.core.exceptions import register_exception_handlers
from api.core.middleware import BASE_PATH, register_middleware
from api.routes import analytics, batch, health, llm, prompts
# TODO: Add agents and transcribe routers after resolving dependency conflicts

app = FastAPI(
    title="Encore API",
    docs_url=f"{BASE_PATH}/docs",
    openapi_url=f"{BASE_PATH}/swagger/common/swagger.json",
)

register_exception_handlers(app)
register_middleware(app)

app.include_router(health)

v1 = APIRouter(prefix=f"{BASE_PATH}/v1")
v1.include_router(llm)
v1.include_router(batch)
v1.include_router(analytics)
v1.include_router(prompts)
app.include_router(v1)

log = LoggingSetup.setup_logger("main")


@app.on_event("startup")
async def initialize_teams() -> None:
    """Ensure default teams exist in LiteLLM."""
    config_file = Path(__file__).parent / "teams.config"
    if not config_file.exists():
        log.info("teams.config not found at %s", config_file)
        return

    try:
        with open(config_file, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f) or {}
    except Exception as exc:  # pragma: no cover - file errors unlikely
        log.error("Failed to read teams.config: %s", exc)
        return

    teams = config.get("teams", [])
    if not teams:
        return

    client = litellm.Client(
        base_url=settings.llm_gateway.base_url,
        api_key=settings.llm_gateway.master_key,
    )

    try:
        resp = client.http.request(method="GET", uri="/team/list")
        existing_teams = {team.get("team_alias") for team in resp if isinstance(team, dict) and team.get("team_alias")}
    except Exception as exc:  # pragma: no cover - network errors
        log.error("Failed to fetch team list: %s", exc)
        return

    for team in teams:
        team_alias = team.get("team_alias")
        if not team_alias or team_alias in existing_teams:
            continue
        payload = {"team_alias": team_alias}
        payload.update({k: v for k, v in team.items() if k != "team_alias"})
        try:
            client.http.request(
                method="POST",
                uri="/team/new",
                json=payload,
                headers={"Content-Type": "application/json"},
            )
            log.info("Created team %s", team_alias)
            key_response = client.http.request(
                method="POST",
                uri="/key/generate",
                json={
                    "team_id": team_alias,
                    "models": team.get("models"),
                    "max_budget": team.get("max_budget"),
                    "duration": team.get("duration"),
                },
                headers={"Content-Type": "application/json"},
            )
            store_in_secrets_manager(f"alpha/{os.getenv('env_name')}/encore/litellm/api-key/{team_alias}", key_response.get("key"))
            log.info("Created key for team %s", team_alias)
        except Exception as exc:  # pragma: no cover - network errors
            log.error("Failed to create team %s: %s", team_alias, exc)
