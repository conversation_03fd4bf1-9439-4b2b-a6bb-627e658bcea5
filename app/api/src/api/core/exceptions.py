"""
Contains custom Exception classes and handlers required for API
"""

import dataclasses

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError


# Exception classes
@dataclasses.dataclass
class BadRequestError(HTTPException):
    """Exception raised for invalid request parameters."""

    def __init__(self, detail: str = "Bad request"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)


@dataclasses.dataclass
class UnauthorizedError(HTTPException):
    """Exception raised for missing or invalid authentication."""

    def __init__(self, detail: str = "Unauthorized"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


@dataclasses.dataclass
class ForbiddenError(HTTPException):
    """Exception raised for access denial."""

    def __init__(self, detail: str = "Forbidden"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)


@dataclasses.dataclass
class NotFoundError(HTTPException):
    """Exception raised when a resource is not found."""

    def __init__(self, detail: str = "Resource not found"):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)


@dataclasses.dataclass
class InternalServerError(HTTPException):
    """Exception raised for server errors."""

    def __init__(self, detail: str = "Internal server error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)


def register_exception_handlers(app: FastAPI):
    """Register exception handlers for the FastAPI application"""

    @app.exception_handler(NotFoundError)
    async def not_found_exception_handler(_request: Request, exc: NotFoundError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
        )

    @app.exception_handler(UnauthorizedError)
    async def unauthorized_exception_handler(_request: Request, exc: UnauthorizedError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=exc.headers,
        )

    # Generic exception handler for all HTTPExceptions
    @app.exception_handler(HTTPException)
    async def http_exception_handler(_request: Request, exc: HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=exc.headers if hasattr(exc, "headers") else None,
        )

    @app.exception_handler(BadRequestError)
    async def bad_request_exception_handler(_request: Request, exc: BadRequestError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
        )

    @app.exception_handler(InternalServerError)
    async def internal_server_exception_handler(_request: Request, exc: InternalServerError):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
        )

    @app.exception_handler(ValidationError)
    async def validation_exception_handler(_request: Request, exc: ValidationError):
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={"detail": exc.errors()},
        )
