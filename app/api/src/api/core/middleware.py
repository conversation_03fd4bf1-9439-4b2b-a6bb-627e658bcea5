"""
Contains custom Middleware classes and definitions required for API
"""

import httpx
import litellm.proxy.client as litellm
from common.logging import LoggingSetup
from common.settings import settings
from fastapi import <PERSON>AP<PERSON>, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.datastructures import MutableHeaders
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

log = LoggingSetup.setup_logger("middleware")

BASE_PATH = "/api/encore"


def register_middleware(app: FastAPI):
    """
    Register middleware for the FastAPI application.
    """
    # add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors.origins.split(";") if settings.cors.origins else [],  # type: ignore
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(
        APIKeyMiddleware,
        exclude_paths={
            "/health/liveness",  # healthcheck
            "/health/readiness",  # healthcheck
            f"{BASE_PATH}/docs",
            f"{BASE_PATH}/openapi.json",
            f"{BASE_PATH}/swagger/common/swagger.json",
        },
    )


class APIKeyMiddleware(BaseHTTPMiddleware):
    """
    Middleware to validate API keys for incoming requests.
    """

    def __init__(self, app: FastAPI, exclude_paths: set[str]):
        """
        Initialize the APIKeyMiddleware with the FastAPI app instance.
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # Skip API key validation for health and docs endpoints
        if request.url.path in self.exclude_paths or request.method == "OPTIONS":
            return await call_next(request)

        # Extract API key from Authorization header
        auth_header = request.headers.get("authorization")
        api_key = None

        if auth_header:
            if auth_header.startswith("Bearer "):
                api_key = auth_header[7:]  # Remove "Bearer " prefix
            else:
                api_key = auth_header

        # Also check for API key in x-api-key header (alternative)
        if not api_key:
            api_key = request.headers.get("x-api-key")

        if not api_key:
            return JSONResponse(
                status_code=401,
                content={"error": "API key required. Provide in Authorization header as 'Bearer <key>' or in x-api-key header"},
            )

        # Validate API key
        if not await self.validate_api_key(api_key):
            return JSONResponse(
                status_code=401,
                content={"error": "Invalid API key"},
            )

        # Store API key in request state for dependency injection
        request.state.api_key = api_key
        response = await call_next(request)
        return response

    async def validate_api_key(self, api_key: str) -> bool:
        """
        Validate the API key.
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{settings.llm_gateway.base_url.rstrip('/')}/key/info", headers={"x-api-key": api_key})
                return response.status_code == 200
        except Exception as e:  # pylint: disable=broad-exception-caught
            log.error("Error validating API key: %s", str(e))
            return False


# --- Middleware Definition ---
class ClientIdToLiteLLMTagsMiddleware(BaseHTTPMiddleware):  # pylint: disable=too-few-public-methods
    """
    FastAPI middleware to transform 'x-encore-id' header to 'x-litellm-tags'.

    This middleware will:
    1. Intercept incoming HTTP requests.
    2. Check if an 'x-encore-id' header is present.
    3. If present, it creates a mutable copy of the request headers.
    4. Sets or updates the 'x-litellm-tags' header in this mutable copy
       with the value from 'x-encore-id'.
    5. Updates the request's internal scope headers with the modified set,
       ensuring that subsequent middleware or the final route handler
       receive the request with the new 'x-litellm-tags' header.
    6. Logs the transformation for debugging purposes.
    """

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        client_id = request.headers.get("x-encore-id")

        if client_id:
            modified_headers = MutableHeaders(request.scope["headers"])
            modified_headers["x-litellm-tags"] = client_id
            request.scope["headers"] = modified_headers.raw
        else:
            log.warning("No 'x-encore-id' header found. Skipping 'x-litellm-tags' transformation.")

        # Proceed to the next middleware in the chain or to the final route handler
        response = await call_next(request)
        return response


def litellm_client():
    """
    Dependency function to create and return LiteLLM proxy client
    """

    async def _get_client(request: Request) -> litellm.Client:
        """
        Dependency function to create and return LiteLLM proxy client
        Uses the API key extracted by middleware
        """
        try:
            api_key = getattr(request.state, "api_key", None)
            log.warning("Extracted API key from request state: %s", request.state)
            if not api_key:
                raise HTTPException(status_code=401, detail="API key not found in request")

            # Create LiteLLM proxy client
            try:
                client = litellm.Client(base_url=settings.llm_gateway.base_url, api_key=api_key)
            except Exception as e:
                log.error("Error creating LiteLLM client: %s", str(e))
                raise HTTPException(status_code=500, detail="Failed to create LiteLLM client") from e

            return client
        except Exception as e:
            log.error("Error creating LiteLLM client: %s", str(e))
            raise HTTPException(status_code=500, detail=f"Failed to create LiteLLM client: {str(e)}") from e

    return _get_client


def litellm_health_client():
    """
    Dependency function to create and return LiteLLM HealthManagementClient
    """

    async def _get_health_client(request: Request) -> litellm.HealthManagementClient:
        """
        Dependency function to create and return LiteLLM HealthManagementClient
        Uses the API key extracted by middleware
        """
        try:
            api_key = getattr(request.state, "api_key", None)
            log.warning("Extracted API key from request state: %s", request.state)
            if not api_key:
                raise HTTPException(status_code=401, detail="API key not found in request")

            # Create LiteLLM HealthManagementClient
            try:
                client = litellm.HealthManagementClient(base_url=settings.llm_gateway.base_url, api_key=api_key)
            except Exception as e:
                log.error("Error creating LiteLLM health client: %s", str(e))
                raise HTTPException(status_code=500, detail="Failed to create LiteLLM health client") from e

            return client
        except Exception as e:
            log.error("Error creating LiteLLM health client: %s", str(e))
            raise HTTPException(status_code=500, detail=f"Failed to create LiteLLM health client: {str(e)}") from e

    return _get_health_client
