"""LLM Router for LiteLLM API integration."""

from typing import Any

import litellm.proxy.client
from common.logging import LoggingSetup
from common.models.llm import (
    ChatCompletionRequest,
    CompletionRequest,
    EmbeddingRequest,
    TokenCountRequest,
)
from fastapi import Depends
from fastapi.routing import APIRouter

from api.core.middleware import litellm_client

log = LoggingSetup.setup_logger("llm_router")
router = APIRouter()

# TODO: can we pass the client GUID in the request.user field?


@router.get(
    "/models",
    tags=["Model Management"],
)
async def model_list(client: litellm.proxy.client.Client = Depends(litellm_client())):
    """Get available models from LiteLLM"""
    return client.models.info()


@router.post(
    "/chat/completions",
    tags=["chat/completions"],
)
async def chat_completion(  # noqa: PLR0915
    request: ChatCompletionRequest, client: litellm.proxy.client.Client = Depends(litellm_client())
) -> Any:
    """Create a chat completion using LiteLLM"""

    # TODO: Handle file uploads if needed

    return client.http.request(  # type: ignore
        method="POST",
        uri="/chat/completions",  # OpenAI-compatible endpoint
        json=request.model_dump(exclude_unset=True),
        headers={"Content-Type": "application/json"},
    )


@router.post(
    "/completions",
    tags=["completions"],
)
async def completion(  # noqa: PLR0915
    request: CompletionRequest, client: litellm.proxy.client.Client = Depends(litellm_client())
) -> Any:
    """Create a text completion using LiteLLM"""
    # TODO: Handle file uploads if needed

    return client.http.request(  # type: ignore
        method="POST",
        uri="/completions",  # OpenAI-compatible endpoint
        json=request.model_dump(exclude_unset=True),
        headers={"Content-Type": "application/json"},
    )


@router.post(
    "/embeddings",
    tags=["embeddings"],
)
async def embeddings(  # noqa: PLR0915
    request: EmbeddingRequest, client: litellm.proxy.client.Client = Depends(litellm_client())
) -> Any:
    """Create embeddings using LiteLLM"""
    return client.http.request(  # type: ignore
        method="POST",
        uri="/embeddings",  # OpenAI-compatible endpoint
        json=request.model_dump(exclude_unset=True),
        headers={"Content-Type": "application/json"},
    )


@router.post(
    "/utils/token_counter",
    tags=["LLM utils"],
)
async def token_counter(request: TokenCountRequest, client: litellm.proxy.client.Client = Depends(litellm_client())) -> Any:
    """Count tokens for given text and model"""
    return client.http.request(  # type: ignore
        method="POST",
        uri="/utils/token_counter",  # OpenAI-compatible endpoint
        json=request.model_dump(exclude_unset=True),
        headers={"Content-Type": "application/json"},
    )
