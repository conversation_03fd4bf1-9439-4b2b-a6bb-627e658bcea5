"""
Tag usage analytics endpoints for tracking LiteLLM tag spend and usage metrics.
"""

from typing import Any, List, Literal, Optional

import litellm.proxy.client
from common.database import get_db_session
from common.logging import LoggingSetup
from common.models.tags import LiteLLMDailyTagSpend, TagUsageByDate, TagUsageByModel, TagUsageDetail, TagUsageResponse, TagUsageSummary
from fastapi import APIRouter, Depends, HTTPException, Query, status
from litellm.proxy._types import LiteLLM_SpendLogs
from sqlalchemy import func, select
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select

from api.core.middleware import litellm_client

log = LoggingSetup.setup_logger("analytics_router")
router = APIRouter(prefix="/analytics")


def apply_date_filters(query: Select[Any], start_date: Optional[str], end_date: Optional[str]) -> Select[Any]:
    """Apply optional date filters to a query."""
    if start_date:
        query = query.where(LiteLLMDailyTagSpend.date >= start_date)  # type: ignore
    if end_date:
        query = query.where(LiteLLMDailyTagSpend.date <= end_date)  # type: ignore
    return query


@router.get(
    "/summary",
    tags=["Tag Analytics"],
    response_model=TagUsageSummary,
    summary="Get tag usage summary",
    description="Get a summary of usage for a specific tag including total spend, requests, and tokens.",
)
async def get_tag_summary(
    tag: str = Query(..., description="Tag to analyze"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    session: Session = Depends(get_db_session),
) -> TagUsageSummary:
    """Get summary statistics for a specific tag."""
    log.info("Getting summary statistics for tag: %s", tag)
    try:
        # Build query with optional date filters
        query = select(
            func.coalesce(func.sum(LiteLLMDailyTagSpend.spend), 0).label("total_spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("total_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
            func.min(LiteLLMDailyTagSpend.date).label("min_date"),
            func.max(LiteLLMDailyTagSpend.date).label("max_date"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        query = apply_date_filters(query, start_date, end_date)  # type: ignore

        result = session.execute(query).first()

        if not result or result.total_requests is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"No data found for tag '{tag}'")

        total_requests = result.total_requests or 0
        successful_requests = result.successful_requests or 0
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0

        return TagUsageSummary(
            tag=tag,
            total_spend=float(result.total_spend or 0),
            total_requests=total_requests,
            total_tokens=int(result.total_tokens or 0),
            success_rate=round(success_rate, 2),
            date_range=f"{result.min_date} to {result.max_date}" if result.min_date and result.max_date else "N/A",
        )

    except Exception as e:
        log.error("Error fetching tag summary for %s: %s", tag, str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch tag summary: {str(e)}") from e


@router.get(
    "/details",
    tags=["Tag Analytics"],
    response_model=List[TagUsageDetail],
    summary="Get detailed tag usage records",
    description="Get detailed usage records for a specific tag with pagination support.",
)
async def get_tag_details(
    tag: str = Query(..., description="Tag to analyze"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    session: Session = Depends(get_db_session),
) -> List[TagUsageDetail]:
    """Get detailed usage records for a specific tag."""
    try:
        query = select(LiteLLMDailyTagSpend).where(LiteLLMDailyTagSpend.tag == tag)
        query = apply_date_filters(query, start_date, end_date)
        query = query.order_by(LiteLLMDailyTagSpend.date.desc(), LiteLLMDailyTagSpend.created_at.desc()).limit(limit).offset(offset)

        results = session.execute(query).scalars().all()

        return [
            TagUsageDetail(
                id=record.id,
                tag=record.tag,
                date=record.date,
                api_key=record.api_key,
                model=record.model,
                model_group=record.model_group,
                custom_llm_provider=record.custom_llm_provider,
                prompt_tokens=record.prompt_tokens,
                completion_tokens=record.completion_tokens,
                cache_read_input_tokens=record.cache_read_input_tokens,
                cache_creation_input_tokens=record.cache_creation_input_tokens,
                spend=float(record.spend),
                api_requests=record.api_requests,
                successful_requests=record.successful_requests,
                failed_requests=record.failed_requests,
                created_at=record.created_at,
                updated_at=record.updated_at,
            )
            for record in results
        ]

    except Exception as e:
        log.error("Error fetching tag details for %s: %s", tag, str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch tag details: {str(e)}") from e


@router.get(
    "/daily",
    tags=["Tag Analytics"],
    response_model=List[TagUsageByDate],
    summary="Get daily tag usage breakdown",
    description="Get daily aggregated usage data for a specific tag.",
)
async def get_tag_daily_usage(
    tag: str = Query(..., description="Tag to analyze"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    session: Session = Depends(get_db_session),
) -> List[TagUsageByDate]:
    """Get daily aggregated usage for a specific tag."""
    try:
        query = select(
            LiteLLMDailyTagSpend.date,
            func.sum(LiteLLMDailyTagSpend.spend).label("spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("api_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        query = apply_date_filters(query, start_date, end_date)
        query = query.group_by(LiteLLMDailyTagSpend.date).order_by(LiteLLMDailyTagSpend.date.desc())

        results = session.execute(query).all()

        return [
            TagUsageByDate(
                date=record.date,
                spend=float(record.spend or 0),
                api_requests=record.api_requests or 0,
                total_tokens=int(record.total_tokens or 0),
                success_rate=round(
                    (record.successful_requests / record.api_requests * 100) if record.api_requests and record.api_requests > 0 else 0, 2
                ),
            )
            for record in results
        ]

    except Exception as e:
        log.error("Error fetching daily tag usage for %s: %s", tag, str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch daily tag usage: {str(e)}") from e


@router.get(
    "/models",
    tags=["Tag Analytics"],
    response_model=List[TagUsageByModel],
    summary="Get tag usage by model",
    description="Get usage breakdown by model for a specific tag.",
)
async def get_tag_model_usage(
    tag: str = Query(..., description="Tag to analyze"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    session: Session = Depends(get_db_session),
) -> List[TagUsageByModel]:
    """Get usage breakdown by model for a specific tag."""
    try:
        query = select(
            LiteLLMDailyTagSpend.model,
            LiteLLMDailyTagSpend.model_group,
            LiteLLMDailyTagSpend.custom_llm_provider,
            func.coalesce(func.sum(LiteLLMDailyTagSpend.spend), 0).label("total_spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("total_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        query = apply_date_filters(query, start_date, end_date)

        query = query.group_by(
            LiteLLMDailyTagSpend.model, LiteLLMDailyTagSpend.model_group, LiteLLMDailyTagSpend.custom_llm_provider
        ).order_by(func.sum(LiteLLMDailyTagSpend.spend).desc())

        results = session.execute(query).all()

        return [
            TagUsageByModel(
                model=record.model or "Unknown",
                model_group=record.model_group or "Unknown",
                custom_llm_provider=record.custom_llm_provider or "Unknown",
                total_spend=float(record.total_spend or 0),
                total_requests=record.total_requests or 0,
                total_tokens=int(record.total_tokens or 0),
                success_rate=round(
                    (record.successful_requests / record.total_requests * 100)
                    if record.total_requests and record.total_requests > 0
                    else 0,
                    2,
                ),
            )
            for record in results
        ]

    except Exception as e:
        log.error("Error fetching model usage for tag %s: %s", tag, str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch model usage: {str(e)}") from e


@router.get(
    "/comprehensive",
    tags=["Tag Analytics"],
    response_model=TagUsageResponse,
    summary="Get comprehensive tag analytics",
    description="Get comprehensive analytics for a specific tag including summary, daily breakdown, and model analysis.",
)
async def get_tag_comprehensive_analytics(
    tag: str = Query(..., description="Tag to analyze"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    session: Session = Depends(get_db_session),
) -> TagUsageResponse:
    """Get comprehensive analytics for a specific tag."""
    try:
        # Get summary
        summary_query = select(  # type: ignore
            func.coalesce(func.sum(LiteLLMDailyTagSpend.spend), 0).label("total_spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("total_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
            func.min(LiteLLMDailyTagSpend.date).label("min_date"),
            func.max(LiteLLMDailyTagSpend.date).label("max_date"),
            func.count().label("total_records"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        summary_query = apply_date_filters(summary_query, start_date, end_date)  # type: ignore

        summary_result = session.execute(summary_query).first()

        if not summary_result or summary_result.total_requests is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"No data found for tag '{tag}'")

        total_requests = summary_result.total_requests or 0
        successful_requests = summary_result.successful_requests or 0
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0

        summary = TagUsageSummary(
            tag=tag,
            total_spend=float(summary_result.total_spend or 0),
            total_requests=total_requests,
            total_tokens=int(summary_result.total_tokens or 0),
            success_rate=round(success_rate, 2),
            date_range=f"{summary_result.min_date} to {summary_result.max_date}"
            if summary_result.min_date and summary_result.max_date
            else "N/A",
        )

        # Get daily data
        daily_query = select(  # type: ignore
            LiteLLMDailyTagSpend.date,
            func.coalesce(func.sum(LiteLLMDailyTagSpend.spend), 0).label("spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("api_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        daily_query = apply_date_filters(daily_query, start_date, end_date)  # type: ignore
        daily_query = daily_query.group_by(LiteLLMDailyTagSpend.date).order_by(LiteLLMDailyTagSpend.date.desc())
        daily_results = session.execute(daily_query).all()

        daily_data = [
            TagUsageByDate(
                date=record.date,
                spend=float(record.spend or 0),
                api_requests=record.api_requests or 0,
                total_tokens=int(record.total_tokens or 0),
                success_rate=round(
                    (record.successful_requests / record.api_requests * 100) if record.api_requests and record.api_requests > 0 else 0, 2
                ),
            )
            for record in daily_results
        ]

        # Get model breakdown
        model_query = select(  # type: ignore
            LiteLLMDailyTagSpend.model,
            LiteLLMDailyTagSpend.model_group,
            LiteLLMDailyTagSpend.custom_llm_provider,
            func.coalesce(func.sum(LiteLLMDailyTagSpend.spend), 0).label("total_spend"),  # type: ignore
            func.sum(LiteLLMDailyTagSpend.api_requests).label("total_requests"),
            func.sum(LiteLLMDailyTagSpend.prompt_tokens + LiteLLMDailyTagSpend.completion_tokens).label("total_tokens"),
            func.sum(LiteLLMDailyTagSpend.successful_requests).label("successful_requests"),
            func.sum(LiteLLMDailyTagSpend.failed_requests).label("failed_requests"),
        ).where(LiteLLMDailyTagSpend.tag == tag)

        model_query = apply_date_filters(model_query, start_date, end_date)  # type: ignore
        model_query = model_query.group_by(
            LiteLLMDailyTagSpend.model, LiteLLMDailyTagSpend.model_group, LiteLLMDailyTagSpend.custom_llm_provider
        ).order_by(func.sum(LiteLLMDailyTagSpend.spend).desc())

        model_results = session.execute(model_query).all()

        model_breakdown = [
            TagUsageByModel(
                model=record.model or "Unknown",
                model_group=record.model_group or "Unknown",
                custom_llm_provider=record.custom_llm_provider or "Unknown",
                total_spend=float(record.total_spend or 0),
                total_requests=record.total_requests or 0,
                total_tokens=int(record.total_tokens or 0),
                success_rate=round(
                    (record.successful_requests / record.total_requests * 100)
                    if record.total_requests and record.total_requests > 0
                    else 0,
                    2,
                ),
            )
            for record in model_results
        ]

        return TagUsageResponse(
            tag=tag,
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=int(summary_result.total_records or 0),
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error("Error fetching comprehensive analytics for tag %s: %s", tag, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch comprehensive analytics: {str(e)}"
        ) from e


@router.get(
    "/tags",
    tags=["Tag Analytics"],
    response_model=List[str],
    summary="List all available tags",
    description="Get a list of all available tags in the system.",
)
async def list_available_tags(session: Session = Depends(get_db_session)) -> List[str]:
    """Get a list of all available tags."""
    try:
        query = select(LiteLLMDailyTagSpend.tag).distinct().order_by(LiteLLMDailyTagSpend.tag).where(LiteLLMDailyTagSpend.tag != "")
        results = session.execute(query).scalars().all()
        return list(results)

    except Exception as e:
        log.error("Error fetching available tags: %s", str(e))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to fetch available tags: {str(e)}") from e


@router.get(
    "/activity",
    tags=["Budget & Spend Tracking"],
    include_in_schema=False,
)
async def get_global_activity(
    start_date: Optional[str] = Query(
        default=None,
        description="Time from which to start viewing key spend",
    ),
    end_date: Optional[str] = Query(
        default=None,
        description="Time till which to view key spend",
    ),
    client: litellm.proxy.client.Client = Depends(litellm_client()),
) -> Any:
    """
    Get number of API Requests, total tokens through proxy

    {
        "daily_data": [
                const chartdata = [
                {
                date: 'Jan 22',
                api_requests: 10,
                total_tokens: 2000
                },
                {
                date: 'Jan 23',
                api_requests: 10,
                total_tokens: 12
                },
        ],
        "sum_api_requests": 20,
        "sum_total_tokens": 2012
    }
    """
    if start_date is None or end_date is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Please provide start_date and end_date"},
        )

    try:
        params = {"start_date": start_date, "end_date": end_date}

        response = client.http.request(  # type: ignore
            method="GET", uri="/global/activity", params=params, headers={"Content-Type": "application/json"}
        )
        return response
    except Exception as e:
        log.error("Error fetching global activity: %s", str(e))
        raise HTTPException(status_code=500, detail=f"Failed to fetch activity data: {str(e)}") from e


@router.get(
    "/activity/model",
    tags=["Budget & Spend Tracking"],
    responses={
        200: {"model": List[LiteLLM_SpendLogs]},
    },
    include_in_schema=False,
)
async def get_global_activity_model(
    start_date: Optional[str] = Query(
        default=None,
        description="Time from which to start viewing spend",
    ),
    end_date: Optional[str] = Query(
        default=None,
        description="Time till which to view spend",
    ),
    client: litellm.proxy.client.Client = Depends(litellm_client()),
) -> Any:
    """
    Get number of API Requests, total tokens through proxy - Grouped by MODEL

    [
        {
            "model": "gpt-4",
            "daily_data": [
                    const chartdata = [
                    {
                    date: 'Jan 22',
                    api_requests: 10,
                    total_tokens: 2000
                    },
                    {
                    date: 'Jan 23',
                    api_requests: 10,
                    total_tokens: 12
                    },
            ],
            "sum_api_requests": 20,
            "sum_total_tokens": 2012

        },
        {
            "model": "azure/gpt-4-turbo",
            "daily_data": [
                    const chartdata = [
                    {
                    date: 'Jan 22',
                    api_requests: 10,
                    total_tokens: 2000
                    },
                    {
                    date: 'Jan 23',
                    api_requests: 10,
                    total_tokens: 12
                    },
            ],
            "sum_api_requests": 20,
            "sum_total_tokens": 2012

        },
    ]
    """
    if start_date is None or end_date is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Please provide start_date and end_date"},
        )

    try:
        params = {"start_date": start_date, "end_date": end_date}

        response = client.http.request(  # type: ignore
            method="GET", uri="/global/activity/model", params=params, headers={"Content-Type": "application/json"}
        )
        return response
    except Exception as e:
        log.error("Error fetching model activity: %s", str(e))
        raise HTTPException(status_code=500, detail=f"Failed to fetch model activity data: {str(e)}") from e


@router.get(
    "/spend/provider",
    tags=["Budget & Spend Tracking"],
    include_in_schema=False,
    responses={
        200: {"model": List[LiteLLM_SpendLogs]},
    },
)
async def get_global_spend_provider(
    start_date: Optional[str] = Query(
        default=None,
        description="Time from which to start viewing spend",
    ),
    end_date: Optional[str] = Query(
        default=None,
        description="Time till which to view spend",
    ),
    client: litellm.proxy.client.Client = Depends(litellm_client()),
) -> Any:
    """
    Get breakdown of spend per provider
    [
        {
            "provider": "Azure OpenAI",
            "spend": 20
        },
        {
            "provider": "OpenAI",
            "spend": 10
        },
        {
            "provider": "VertexAI",
            "spend": 30
        }
    ]
    """
    if start_date is None or end_date is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Please provide start_date and end_date"},
        )

    try:
        params = {"start_date": start_date, "end_date": end_date}

        response = client.http.request(  # type: ignore
            method="GET", uri="/global/spend/provider", params=params, headers={"Content-Type": "application/json"}
        )
        return response
    except Exception as e:
        log.error("Error fetching provider spend: %s", str(e))
        raise HTTPException(status_code=500, detail=f"Failed to fetch provider spend data: {str(e)}") from e


@router.get(
    "/spend/report",
    tags=["Budget & Spend Tracking"],
    responses={
        200: {"model": List[LiteLLM_SpendLogs]},
    },
)
async def get_global_spend_report(
    start_date: Optional[str] = Query(
        default=None,
        description="Time from which to start viewing spend",
    ),
    end_date: Optional[str] = Query(
        default=None,
        description="Time till which to view spend",
    ),
    group_by: Optional[Literal["team", "customer", "api_key"]] = Query(
        default="team",
        description="Group spend by internal team or customer or api_key",
    ),
    api_key: Optional[str] = Query(
        default=None,
        description="View spend for a specific api_key. Example api_key='sk-1234",
    ),
    internal_user_id: Optional[str] = Query(
        default=None,
        description="View spend for a specific internal_user_id. Example internal_user_id='1234",
    ),
    team_id: Optional[str] = Query(
        default=None,
        description="View spend for a specific team_id. Example team_id='1234",
    ),
    customer_id: Optional[str] = Query(
        default=None,
        description="View spend for a specific customer_id. Example customer_id='1234. Can be used in conjunction with team_id as well.",
    ),
    client: litellm.proxy.client.Client = Depends(litellm_client()),
) -> Any:
    """
    Get Daily Spend per Team, based on specific startTime and endTime. Per team, view usage by each key, model
    [
        {
            "group-by-day": "2024-05-10",
            "teams": [
                {
                    "team_name": "team-1"
                    "spend": 10,
                    "keys": [
                        "key": "1213",
                        "usage": {
                            "model-1": {
                                    "cost": 12.50,
                                    "input_tokens": 1000,
                                    "output_tokens": 5000,
                                    "requests": 100
                                },
                                "audio-modelname1": {
                                "cost": 25.50,
                                "seconds": 25,
                                "requests": 50
                        },
                        }
                    }
            ]
        ]
    }
    """
    if start_date is None or end_date is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Please provide start_date and end_date"},
        )

    try:
        params = {"start_date": start_date, "end_date": end_date}
        if group_by:
            params["group_by"] = group_by
        if api_key:
            params["api_key"] = api_key
        if internal_user_id:
            params["internal_user_id"] = internal_user_id
        if team_id:
            params["team_id"] = team_id
        if customer_id:
            params["customer_id"] = customer_id

        response = client.http.request(  # type: ignore
            method="GET", uri="/global/spend/report", params=params, headers={"Content-Type": "application/json"}
        )
        return response
    except Exception as e:
        log.error("Error fetching global spend report: %s", str(e))
        raise HTTPException(status_code=500, detail=f"Failed to fetch spend report: {str(e)}") from e
