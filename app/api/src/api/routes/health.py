"""
Health Check Router Module

This module provides health check endpoints similar to the LLM Gateway's health endpoints.
Includes liveness and readiness checks for the API service.
"""

import os
import time
from typing import Any

import litellm.proxy.client as litellm
from common.logging import LoggingSetup
from common.settings import settings
from fastapi import APIRouter, Depends, HTTPException, status

from api.core.middleware import litellm_client, litellm_health_client

log = LoggingSetup.setup_logger("health_router")
router = APIRouter()


@router.get(
    "/",
    tags=["health"],
)
@router.get(
    "/health",
    tags=["health"],
)
@router.get(
    "/health/liveness",
    tags=["health"],
)
async def liveness_check() -> dict[str, Any]:
    """
    Liveness probe endpoint - mirrors LLM Gateway /health/liveness

    This endpoint indicates whether the application is running.
    It should return 200 if the application is alive, regardless of dependencies.

    Returns:
        Dict containing status and timestamp
    """
    return {
        "status": "alive",
        "service": "encore-api",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
    }


@router.get(
    "/health/readiness",
    tags=["health"],
    dependencies=[Depends(litellm_client)],
)
async def readiness_check(
    client: litellm.HealthManagementClient = Depends(litellm_health_client()),
) -> dict[str, Any]:
    """
    Readiness probe endpoint - checks if service is ready to serve traffic

    This endpoint should check critical dependencies like database connectivity,
    external services, etc. Returns 200 only if all dependencies are healthy.

    Returns:
        Dict containing status, checks, and timestamp

    Raises:
        HTTPException: 503 if any critical dependency is unhealthy
    """
    checks = {}
    overall_status = "ready"

    try:
        checks["database"] = await _check_database()
        checks["llm_gateway"] = await _check_llm_gateway(client)
        checks["s3_storage"] = await _check_s3_storage()

    except Exception as e:  # pylint: disable=broad-exception-caught
        log.error("Health check failed: %s", e)
        overall_status = "not_ready"
        checks["error"] = str(e)

    if any(check.get("status") not in ["connected", "healthy"] for check in checks.values() if isinstance(check, dict)):
        overall_status = "not_ready"
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": overall_status,
                "service": "encore-api",
                "checks": checks,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
            },
        )

    return {
        "status": overall_status,
        "service": "encore-api",
        "checks": checks,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
    }


async def _check_database() -> dict[str, Any]:
    """
    Check database connectivity

    Returns:
        Dict with status and details
    """
    try:
        # TODO: test actual DB connectivity
        if settings.database.connection_string:
            return {"status": "healthy", "message": "Database configuration available"}
        return {"status": "unhealthy", "message": "Database connection string not configured"}
    except Exception as e:  # pylint: disable=broad-exception-caught
        log.warning("Database health check failed: %s", e)
        return {"status": "unhealthy", "message": f"Database check failed: {str(e)}"}


async def _check_llm_gateway(client: litellm.HealthManagementClient) -> dict[str, Any]:
    """
    Check LLM Gateway connectivity

    Returns:
        Dict with status and details
    """
    return client.get_readiness()


async def _check_s3_storage() -> dict[str, Any]:
    """
    Check S3 storage connectivity

    Returns:
        Dict with status and details
    """
    try:
        aws_region = os.environ.get("AWS_REGION")

        if aws_region:
            return {
                "status": "healthy",
                "message": "AWS configuration available",
                "region": aws_region,
            }
        return {"status": "warning", "message": "AWS region not configured"}

    except Exception as e:  # pylint: disable=broad-exception-caught
        log.warning("S3 storage health check failed: %s", e)
        return {"status": "unhealthy", "message": f"S3 storage check failed: {str(e)}"}
