from __future__ import annotations

import json
import uuid
from datetime import datetime, timezone
from typing import TYPE_CHECKING, Annotated

import boto3
from common.logging import LoggingSetup
from common.models import TranscriptionJobResponse, TranscriptionRequest
from common.s3_storage import S3Storage
from common.services.job_store import BatchJobStatusEnum, JobStore
from common.settings import settings
from fastapi import APIRouter, Depends, HTTPException, UploadFile, status

if TYPE_CHECKING:
    from mypy_boto3_sqs import SQSClient

log = LoggingSetup.setup_logger("transcribe_router")
router = APIRouter()


@router.post("/transcribe", tags=["Transcription"], response_model=TranscriptionJobResponse)
async def transcribe(
    request: Annotated[TranscriptionRequest, Depends(TranscriptionRequest.as_form)],
) -> TranscriptionJobResponse:
    """Upload media file or enqueue transcription from presigned URL."""
    job_id = uuid.uuid4()

    try:
        if isinstance(request.file, UploadFile):
            storage = S3Storage(prefix=f"transcription/jobs/{job_id}")
            data = await request.file.read()
            extension = request.file.filename.split(".")[-1] if request.file.filename else ""
            key = f"{job_id}/{request.file.filename}"
            storage.upload_file(
                key,
                data,
                request.file.content_type or "application/octet-stream",
            )
            request = request.model_copy(
                update={
                    "file": f"s3://{storage.bucket_name}/{key}",
                    "media_format": extension,
                }
            )
        else:
            trimmed = str(request.file).split("?")[0]
            extension = trimmed.split(".")[-1] if "." in trimmed else ""
            request = request.model_copy(update={"media_format": extension})

        sqs: SQSClient = boto3.client("sqs")
        sqs.send_message(
            QueueUrl=settings.task_processor.task_queue_namespace,
            MessageBody=json.dumps(request.model_dump(exclude_none=True)),
            MessageGroupId=request.user or "",
            MessageDeduplicationId=str(uuid.uuid4()),
            MessageAttributes={
                "JobId": {"StringValue": str(job_id), "DataType": "String"},
                "MessageGroupId": {"StringValue": request.user or "", "DataType": "String"},
                "MessageType": {"StringValue": type(request).__name__, "DataType": "String"},
                "BatchSequence": {"StringValue": "1", "DataType": "Number"},
                "BatchTotal": {"StringValue": "1", "DataType": "Number"},
                "Timestamp": {"StringValue": datetime.now(tz=timezone.utc).isoformat(), "DataType": "String"},
            },
        )

        job_store = JobStore.get_instance()
        await job_store.async_create_job(
            client_guid=uuid.UUID(request.user) if request.user else None,
            total_count=1,
            job_params=None,
            status=BatchJobStatusEnum.PENDING,
        )

        return TranscriptionJobResponse(job_id=str(job_id))
    except Exception as e:  # pylint: disable=broad-exception-caught
        log.error("Failed to enqueue transcription task: %s", e, exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to enqueue task") from e
