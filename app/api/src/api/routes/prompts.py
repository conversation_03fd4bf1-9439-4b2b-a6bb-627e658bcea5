from __future__ import annotations

import uuid
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from fastapi import APIRouter, HTTPException, Query, status

from common.logging import LoggingSetup
from common.services.prompt_store import PromptStore

router = APIRouter()
log = LoggingSetup.setup_logger("prompts_router")

_PROMPT_CACHE: Dict[int, str] = {}
_PROMPT_DIR = Path(__file__).resolve().parent.parent / "prompts"


def _load_default_prompts() -> None:
    if _PROMPT_CACHE:
        return
    for path in _PROMPT_DIR.glob("*.yaml"):
        try:
            text = path.read_text(encoding="utf-8")
            data = yaml.safe_load(text)
            prompt_id = int(data["id"])
            _PROMPT_CACHE[prompt_id] = text
        except Exception as exc:  # pylint: disable=broad-exception-caught
            log.warning("Failed loading prompt %s: %s", path, exc)


_load_default_prompts()


@router.get("/prompts", tags=["prompts"])
async def list_prompts(client_guid: Optional[uuid.UUID] = Query(default=None)) -> Any:
    if client_guid is None:
        return [{"id": pid, "prompt": prompt} for pid, prompt in _PROMPT_CACHE.items()]

    store = PromptStore.get_instance()
    prompts = await store.async_list_prompts(client_guid)
    return [prompt.__dict__ for prompt in prompts]


@router.get("/prompts/{prompt_id}", tags=["prompts"])
async def get_prompt(prompt_id: int, client_guid: Optional[uuid.UUID] = Query(default=None)) -> Any:
    if client_guid is None:
        prompt = _PROMPT_CACHE.get(prompt_id)
        if prompt is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Prompt not found")
        return {"id": prompt_id, "prompt": prompt}

    store = PromptStore.get_instance()
    try:
        result = await store.async_get_prompt(prompt_id, client_guid)
    except LookupError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Prompt not found") from exc
    return result.__dict__


@router.post("/prompts", tags=["prompts"])
async def create_prompt(client_guid: Optional[uuid.UUID], prompt: str) -> Any:
    if client_guid is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="client_guid required")

    store = PromptStore.get_instance()
    prompt_id = await store.async_create_prompt(client_guid, prompt)
    return {"id": prompt_id}


@router.put("/prompts/{prompt_id}", tags=["prompts"])
async def update_prompt(prompt_id: int, client_guid: Optional[uuid.UUID], prompt: str) -> Any:
    if client_guid is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="client_guid required")
    store = PromptStore.get_instance()
    try:
        await store.async_update_prompt(prompt_id, client_guid, prompt)
    except LookupError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Prompt not found") from exc
    return {"status": "updated"}


@router.delete("/prompts/{prompt_id}", tags=["prompts"])
async def delete_prompt(prompt_id: int, client_guid: Optional[uuid.UUID]) -> Any:
    if client_guid is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="client_guid required")
    store = PromptStore.get_instance()
    try:
        await store.async_delete_prompt(prompt_id, client_guid)
    except LookupError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Prompt not found") from exc
    return {"status": "deleted"}

