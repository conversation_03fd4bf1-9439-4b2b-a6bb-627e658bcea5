"""
LLM Router Module

This module provides FastAPI router endpoints for Large Language Model operations.
The module interfaces with LlmService to handle the actual LLM operations.
"""

import uuid
from datetime import datetime, timezone
from typing import TYPE_CHECKING, List

import boto3
from common.logging import LoggingSetup
from common.models.batch import JobResponse, JobResultResponse
from common.models.llm import CompletionRequest
from common.s3_storage import get_s3_storage_for_job_results
from common.services.job_store import BatchJobStatusEnum, Job, JobStore
from common.settings import settings
from fastapi import APIRouter, HTTPException
from fastapi import status as status_code

if TYPE_CHECKING:
    from mypy_boto3_sqs import SQSClient

log = LoggingSetup.setup_logger("batch_router")
router = APIRouter()


@router.post(
    "/batch",
    tags=["Batch Jobs"],
    response_model=JobResponse,
)
async def create_batch_job(
    payload: List[CompletionRequest],  # TODO: should be Union[CompletionRequest, DocumentRequest, TranscriptionRequest, etc]
):
    """
    Generate a batch of text outputs based on the prompt template and inputs from location

    Args:
        payload: Generation request parameters

    Returns:
        JobResponse with batch job information

    Raises:
        HTTPException: If there's an error creating the batch job
    """
    batch_size = len(payload)
    if batch_size == 0:
        return None  # TODO

    sqs: SQSClient = boto3.client("sqs")

    job_id = uuid.uuid4()

    job_params = None
    if settings.task_processor.store_job_params:
        job_params = payload.__dict__

    first_task = payload[0]

    job_store = JobStore.get_instance()
    await job_store.async_create_job(
        client_guid=uuid.UUID(first_task.user) if first_task.user else None,
        total_count=batch_size,
        job_params=job_params,
        status=BatchJobStatusEnum.PENDING,
    )

    for i, request in enumerate(payload, 1):
        sqs.send_message(
            QueueUrl=settings.task_processor.task_queue_namespace,
            MessageBody=request.model_dump_json(exclude_none=True),  # TODO: handle large payloads
            MessageGroupId=request.user or "",  # Use user as MessageGroupId for FIFO
            MessageDeduplicationId=str(uuid.uuid4()),
            MessageAttributes={
                "JobId": {"StringValue": str(job_id), "DataType": "String"},
                "MessageGroupId": {"StringValue": request.user or "", "DataType": "String"},
                "MessageType": {"StringValue": type(request).__name__, "DataType": "String"},
                "BatchSequence": {"StringValue": str(i), "DataType": "Number"},
                "BatchTotal": {"StringValue": str(batch_size), "DataType": "Number"},
                "Timestamp": {"StringValue": datetime.now(tz=timezone.utc).isoformat(), "DataType": "String"},
            },
        )

    return JobResponse(job_id=job_id)


@router.get("/batch/{job_id}/results", tags=["Batch Jobs"])
async def get_batch_results(job_id: str, page: int = 1, page_size: int = 50):
    """
    Get download URLs for all results in a batch job with pagination

    Args:
        request: FastAPI request object
        job_id: UUID of the job
        page: Page number (starting from 1)
        page_size: Number of items per page

    Returns:
        Dictionary with pre-signed URLs for each result file and pagination info

    Raises:
        HTTPException: If the batch job is not found or not completed
    """
    log.info("get_batch_results request for job_id: %s", job_id)

    try:
        # Validate pagination parameters
        if page < 1 or page_size < 1:
            raise HTTPException(status_code=status_code.HTTP_400_BAD_REQUEST, detail="Invalid pagination parameters")

        job_store = JobStore.get_instance()
        job = await job_store.async_get_job(job_id=uuid.UUID(job_id))

        status = job.status
        status_text = BatchJobStatusEnum(status).name.lower()
        status_description = job.status_description

        response = JobResultResponse(
            job_id=job_id,
            status=status_text,
        )

        if status in [
            BatchJobStatusEnum.COMPLETED.value,
            BatchJobStatusEnum.PARTIALLY_COMPLETED.value,
        ]:
            presigned_urls, pagination = prepare_presigned_urls(
                job=job,
                page=page,
                page_size=page_size,
            )
            response.result_urls = presigned_urls
            response.pagination = pagination
        else:
            if status_description:
                response.status_description = status_description

        return response
    except HTTPException:
        raise
    except Exception as e:
        log.error("Error getting batch results: %s", e, exc_info=True)
        raise HTTPException(
            status_code=status_code.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting batch results",
        ) from e


def prepare_presigned_urls(job: Job, page: int, page_size: int):  # pylint: disable=too-many-locals
    """
    Prepare pre-signed URLs for the results of a batch job with pagination.

    Args:
        job_id (str): The ID of the batch job.
        job_data (dict): The job data containing the result mapping.
        page (int): The current page number.
        page_size (int): The number of items per page.

    Returns:
        tuple: A tuple containing a list of dictionaries with pre-signed URLs and pagination info.
    """
    presigned_urls = []
    s3_storage = get_s3_storage_for_job_results(job.id)

    # Apply pagination
    items = job.results
    total_items = len(items)
    total_pages = (total_items + page_size - 1) // page_size
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_items)

    # Get only items for current page
    for item_id, result_files in items[start_idx:end_idx]:
        item_dict = {
            "file_path": item_id,
            "urls": [],
        }
        for result_file in result_files:
            _s3_key = f"{s3_storage.prefix}/{result_file}"
            _s3_key_error = _s3_key.replace(".json", ".error.json")
            if s3_storage.exists(_s3_key):
                url = s3_storage.get_presigned_url(_s3_key)
                item_dict["urls"].append(url)
                log.info("Item completed: %s", item_id)
            elif s3_storage.exists(_s3_key_error):
                url = s3_storage.get_presigned_url(_s3_key_error)
                item_dict["urls"].append(url)
                log.warning("Item failed: %s", item_id)
            else:
                log.warning("Item missing: %s", item_id)
        presigned_urls.append(item_dict)

    pagination = {
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "total_items": total_items,
    }
    return presigned_urls, pagination
