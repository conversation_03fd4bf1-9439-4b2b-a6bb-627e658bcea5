"""Router exposing basic agent management."""

from __future__ import annotations

import uuid
from typing import TYPE_CHECKING, Any, Dict

from common.logging import LoggingSetup
from common.services.agent_store import AgentStore
from fastapi import APIRouter, Depends, HTTPException, status

from api.core.middleware import litellm_client

if TYPE_CHECKING:  # pragma: no cover - for type checking only
    from litellm.proxy.client import Client


log = LoggingSetup.setup_logger("agents_router")
router = APIRouter()


@router.post("/agent", tags=["Agents"], status_code=status.HTTP_201_CREATED)
async def create_agent(definition: str) -> Dict[str, str]:
    """Create a workflow from YAML and return its identifier."""

    store = AgentStore.get_instance()
    workflow_id = await store.async_create_agent(
        name="agent",
        definition=definition,
    )
    return {"agent_id": str(workflow_id)}


@router.post("/agent/{agent_id}/execute", tags=["Agents"])
async def execute_agent(
    agent_id: str,
    inputs: Dict[str, Any],
    client: "Client" = Depends(litellm_client()),
) -> Any:
    """Execute the stored workflow with provided inputs."""

    try:
        uid = uuid.UUID(agent_id)
    except ValueError as exc:  # pragma: no cover - validated runtime
        log.error("Invalid agent id: %s", agent_id)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid agent id") from exc

    store = AgentStore.get_instance()
    try:
        _ = await store.async_get_agent(uid)  # TODO: get the agent definition
    except LookupError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Agent not found") from None

    try:
        result = None  # run agent with (inputs=inputs)
    except Exception as exc:  # pragma: no cover - runtime error
        log.error("Agent execution failed: %s", str(exc))
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Execution failed") from exc

    return {"result": result}
