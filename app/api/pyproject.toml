[project]
name = "api"
version = "0.1.0"
description = "FastAPI service"
requires-python = ">=3.11"
dependencies = [
    "encore-ai",
    "common",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pyyaml>=6.0",
]

[tool.uv.sources]
common = { workspace = true }
encore-ai = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]
