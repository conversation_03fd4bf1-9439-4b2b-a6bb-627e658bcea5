"""Module for handling S3 storage operations for batch job results."""

import json
import uuid
from typing import TYPE_CHECKING, Any, Optional

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

from common.logging import LoggingSetup
from common.settings import settings

if TYPE_CHECKING:
    from mypy_boto3_s3 import S3Client

log = LoggingSetup.setup_logger("s3_storage")


class S3Storage:
    """Handler for S3 storage operations."""

    s3_client: "S3Client"

    def __init__(
        self,
        bucket_name: Optional[str] = settings.task_processor.bucket_name,
        prefix: Optional[str] = "batch-jobs",
    ):
        """Initialize S3 storage handler.

        Args:
            bucket_name: S3 bucket name
            region_name: AWS region
            prefix: Prefix for all S3 keys (folder path)
        """
        self.bucket_name = bucket_name
        self.prefix = prefix.rstrip("/")
        config = Config(signature_version="s3v4")
        self.s3_client = boto3.client("s3", config=config)

    def exists(self, s3_key: str) -> bool:
        """Check if an S3 object exists.

        Args:
            s3_key: S3 key to check

        Returns:
            bool: True if the object exists, False otherwise
        """
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            log.error("Error checking existence of %s: %s", s3_key, e, exc_info=True)
            raise
        except Exception as e:
            log.error("Error checking existence of %s: %s", s3_key, e, exc_info=True)
            raise

    def get_job_prefix(self, job_id: str) -> str:
        """Get the S3 key prefix for a batch job.

        Args:
            job_id: UUID of the job

        Returns:
            S3 key prefix for the batch job
        """
        return f"{self.prefix}/jobs/{job_id}"

    def store_result(self, s3_key: str, result_data: dict[str, Any]) -> str:
        """Store a single result in S3.

        Args:
            s3_key: s3 key
            result_data: Result data to store

        Raises:
            Exception: If there's an error storing the result
        """
        try:
            # Convert result to JSON
            result_json = json.dumps(result_data)

            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=result_json,
                ContentType="application/json",
            )

        except Exception as e:
            log.error("Error storing result for message %s: %s", s3_key, e, exc_info=True)
            raise

    def upload_file(self, s3_key: str, data: bytes, content_type: str) -> None:
        """Upload raw bytes to S3 under the specified key.

        Args:
            s3_key: S3 key
            data: File data to upload
            content_type: MIME type of the file
        """
        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=data,
                ContentType=content_type,
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            log.error("Error uploading %s: %s", s3_key, e, exc_info=True)
            raise

    def get_presigned_url(self, s3_key: str, expiration: Optional[int] = 3600) -> Optional[str]:
        """Generate a pre-signed URL for a result file.

        Args:
            item_id: Unique identifier for the item
            expiration: URL expiration time in seconds

        Returns:
            Pre-signed URL or None if error
        """
        try:
            url = self.s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": self.bucket_name, "Key": s3_key},
                ExpiresIn=expiration,
            )

            return url

        except ClientError as e:
            log.error("Error generating presigned URL: %s", e, exc_info=True)
            return None


def get_s3_storage(prefix: str) -> S3Storage:
    """Get a configured S3Storage instance.

    Returns:
        S3Storage: Configured S3Storage instance
    """
    return S3Storage(prefix=prefix)


def get_s3_storage_for_job(job_id: str) -> S3Storage:
    """Get a configured S3Storage instance for a specific batch job.

    Args:
        job_id: UUID of the batch job

    Returns:
        S3Storage: Configured S3Storage instance
    """
    return S3Storage(prefix=f"batch-jobs/jobs/{job_id}")


def get_s3_storage_for_job_results(job_id: uuid.UUID) -> S3Storage:
    """Get a configured S3Storage instance for a specific batch job's results.

    Args:
        job_id: UUID of the batch job

    Returns:
        S3Storage: Configured S3Storage instance
    """
    return S3Storage(prefix=f"batch-jobs/jobs/{job_id}/results")
