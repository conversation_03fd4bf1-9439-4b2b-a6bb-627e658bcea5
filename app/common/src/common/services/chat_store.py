"""
This module provides the implementation for storing and retrieving chat messages
using SQLAlchemy with support for PostgreSQL. It includes the definition of the
data model, session management, and methods for interacting with the chat store.

Classes:
    ChatStore: A class for managing chat messages in the database.
    ChatMessage: A Pydantic model representing a chat message.

Functions:
    get_data_model: Returns the SQLAlchemy data model for the chat store.

Usage example:
    chat_store = PostgresChatStore(database_url)
    chat_store.add_message(client_guid, user_guid, chat_message)
    messages = chat_store.get_messages(client_guid, user_guid)
"""

import uuid
from typing import Any, Optional
from urllib.parse import urlparse

# TODO: do we really need to import them from llama_index?
from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.llms import ChatMessage
from llama_index.core.schema import BaseComponent
from sqlalchemy import (
    Column,
    Index,
    Integer,
    UniqueConstraint,
    create_engine,
    delete,
    select,
    text,
)
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>, JSONB, TIMESTAMP, UUID
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from common.utils import get_current_time


def get_data_model(
    base: type,
    index_name: str,
    schema_name: str,
    use_jsonb: bool = False,
) -> Any:
    """
    This part create a dynamic sqlalchemy model with a new table.
    """
    tablename = f"{index_name}"  # dynamic table name
    class_name = f"Data{index_name}"  # dynamic class name

    chat_dtype = JSONB if use_jsonb else JSON

    class AbstractData(base):  # pylint: disable=too-few-public-methods
        """Model for chat messages."""

        __abstract__ = True  # this line is necessary
        id = Column(Integer, primary_key=True, autoincrement=True)  # Add primary key
        client_guid = Column(UUID, nullable=False)
        user_guid = Column(UUID, nullable=False)
        message = Column(chat_dtype)
        timestamp = Column(TIMESTAMP, nullable=False)

    return type(
        class_name,
        (AbstractData,),
        {
            "__tablename__": tablename,
            "__table_args__": (
                UniqueConstraint("id", name=f"{tablename}:unique_id"),
                Index(f"{tablename}:idx_id", "id"),
                Index(
                    f"{tablename}:idx_client_user_timestamp",
                    "client_guid",
                    "user_guid",
                    "timestamp",
                ),
                {"schema": schema_name},
            ),
        },
    )


class PostgresChatStore(BaseComponent):
    """
    This class provides methods for storing and retrieving chat messages
    using a PostgreSQL database with SQLAlchemy.

    Attributes:
        uri (str): The database URI for connecting to the PostgreSQL database.
        engine (Engine): The SQLAlchemy engine instance.
        Session (sessionmaker): The SQLAlchemy session factory.
    """

    table_name: Optional[str] = Field(default="chatstore", description="Postgres table name.")
    schema_name: Optional[str] = Field(default="public", description="Postgres schema name.")

    _table_class: Optional[Any] = PrivateAttr()
    _session: Optional[sessionmaker] = PrivateAttr()
    _async_session: Optional[sessionmaker] = PrivateAttr()

    def __init__(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        session: sessionmaker,
        async_session: sessionmaker,
        table_name: str,
        schema_name: str = "public",
        use_jsonb: bool = False,
    ):
        super().__init__(
            table_name=table_name.lower(),
            schema_name=schema_name.lower(),
        )

        # sqlalchemy model
        base = declarative_base()
        self._table_class = get_data_model(
            base,
            table_name,
            schema_name,
            use_jsonb=use_jsonb,
        )
        self._session = session
        self._async_session = async_session
        self._initialize(base)

    # pylint: disable=too-many-arguments, too-many-positional-arguments, too-many-locals
    @classmethod
    def from_params(
        cls,
        host: Optional[str] = None,
        port: Optional[str] = None,
        database: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        table_name: str = "chatstore",
        schema_name: str = "public",
        connection_string: Optional[str] = None,
        async_connection_string: Optional[str] = None,
        debug: bool = False,
        use_jsonb: bool = False,
    ) -> "PostgresChatStore":
        """Return connection string from database parameters."""
        conn_str = connection_string or f"postgresql+psycopg://{user}:{password}@{host}:{port}/{database}"
        async_conn_str = async_connection_string or (f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}")
        session, async_session = cls._connect(conn_str, async_conn_str, debug)
        return cls(
            session=session,
            async_session=async_session,
            table_name=table_name,
            schema_name=schema_name,
            use_jsonb=use_jsonb,
        )

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    @classmethod
    def from_uri(
        cls,
        uri: str,
        db_name: str,
        table_name: str = "chatstore",
        schema_name: str = "public",
        debug: bool = False,
        use_jsonb: bool = False,
    ) -> "PostgresChatStore":
        """Return connection string from database parameters."""
        result = urlparse(uri)
        params = {
            "user": result.username,
            "password": result.password,
            "host": result.hostname,
            "port": result.port if result.port else 5432,
        }
        return cls.from_params(
            **params,
            database=db_name,
            table_name=table_name,
            schema_name=schema_name,
            debug=debug,
            use_jsonb=use_jsonb,
        )

    @classmethod
    def _connect(cls, connection_string: str, async_connection_string: str, debug: bool) -> tuple[sessionmaker, sessionmaker]:
        _engine = create_engine(connection_string, echo=debug)
        session = sessionmaker(_engine)

        _async_engine = create_async_engine(async_connection_string)
        async_session = sessionmaker(_async_engine, class_=AsyncSession)
        return session, async_session

    def _create_schema_if_not_exists(self) -> None:
        with self._session() as session, session.begin():
            # Check if the specified schema exists with "CREATE" statement
            check_schema_statement = text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{self.schema_name}'")
            result = session.execute(check_schema_statement).fetchone()

            # If the schema does not exist, then create it
            if not result:
                create_schema_statement = text(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name}")
                session.execute(create_schema_statement)

            session.commit()

    def _create_tables_if_not_exists(self, base) -> None:
        with self._session() as session, session.begin():
            base.metadata.create_all(session.connection())

    def _initialize(self, base) -> None:
        self._create_schema_if_not_exists()
        self._create_tables_if_not_exists(base)

    def set_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID, messages: list[ChatMessage]) -> None:
        """Set messages for a client-user pair."""
        with self._session() as session:
            # First delete existing messages for this client-user pair
            stmt = delete(self._table_class).where(
                self._table_class.client_guid == client_guid,
                self._table_class.user_guid == user_guid,
            )
            session.execute(stmt)

            # Then insert new messages
            for message in messages:
                new_row = self._table_class(
                    client_guid=client_guid,
                    user_guid=user_guid,
                    message=message.model_dump(),
                    timestamp=get_current_time(),
                )
                session.add(new_row)

            session.commit()

    async def aset_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID, messages: list[ChatMessage]) -> None:
        """Async version of set_messages."""
        async with self._async_session() as session:
            # First delete existing messages
            stmt = delete(self._table_class).where(
                self._table_class.client_guid == client_guid,
                self._table_class.user_guid == user_guid,
            )
            await session.execute(stmt)

            # Then insert new messages
            for message in messages:
                new_row = self._table_class(
                    client_guid=client_guid,
                    user_guid=user_guid,
                    message=message.model_dump(),
                    timestamp=get_current_time(),
                )
                session.add(new_row)

            await session.commit()

    def get_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID, top: int = -1) -> list[ChatMessage]:
        """Get messages for a client-user pair."""
        with self._session() as session:
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp)
            )

            if top > 0:
                stmt = stmt.limit(top)

            result = session.execute(stmt).scalars().all()
            return [ChatMessage.model_validate(row.message) for row in result]

    async def aget_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID, top: int = -1) -> list[ChatMessage]:
        """Async version of get_messages."""
        async with self._async_session() as session:
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp)
            )

            if top > 0:
                stmt = stmt.limit(top)

            result = await session.execute(stmt)
            rows = result.scalars().all()
            return [ChatMessage.model_validate(row.message) for row in rows]

    def add_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID, message: ChatMessage) -> None:
        """Add a message for a client-user pair."""
        with self._session() as session:
            new_row = self._table_class(
                client_guid=client_guid,
                user_guid=user_guid,
                message=message.model_dump(),
                timestamp=get_current_time(),
            )
            session.add(new_row)
            session.commit()

    async def aadd_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID, message: ChatMessage) -> None:
        """Async version of add_message."""
        async with self._async_session() as session:
            new_row = self._table_class(
                client_guid=client_guid,
                user_guid=user_guid,
                message=message.model_dump(),
                timestamp=get_current_time(),
            )
            session.add(new_row)
            await session.commit()

    def delete_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID) -> Optional[list[ChatMessage]]:
        """Delete all messages for a client-user pair."""
        messages = self.get_messages(client_guid, user_guid)
        with self._session() as session:
            stmt = delete(self._table_class).where(
                self._table_class.client_guid == client_guid,
                self._table_class.user_guid == user_guid,
            )
            session.execute(stmt)
            session.commit()
        return messages

    async def adelete_messages(self, client_guid: uuid.UUID, user_guid: uuid.UUID) -> Optional[list[ChatMessage]]:
        """Async version of delete_messages."""
        messages = await self.aget_messages(client_guid, user_guid)
        async with self._async_session() as session:
            stmt = delete(self._table_class).where(
                self._table_class.client_guid == client_guid,
                self._table_class.user_guid == user_guid,
            )
            await session.execute(stmt)
            await session.commit()
        return messages

    def delete_last_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID) -> Optional[ChatMessage]:
        """Delete the last message for a client-user pair."""
        with self._session() as session:
            # Get the last message by timestamp
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp.desc())
                .limit(1)
            )

            result = session.execute(stmt).scalar_one_or_none()
            if not result:
                return None

            deleted_message = ChatMessage.model_validate(result.message)
            session.delete(result)
            session.commit()

            return deleted_message

    async def adelete_last_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID) -> Optional[ChatMessage]:
        """Async version of delete_last_message."""
        async with self._async_session() as session:
            # Get the last message by timestamp
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp.desc())
                .limit(1)
            )

            result = await session.execute(stmt)
            row = result.scalar_one_or_none()
            if not row:
                return None

            deleted_message = ChatMessage.model_validate(row.message)
            session.delete(row)
            await session.commit()

            return deleted_message

    def update_last_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID, **data: Any) -> Optional[ChatMessage]:
        """Update the last message for a client-user pair and preserve the old message."""
        with self._session() as session:
            # Get the last message by timestamp
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp.desc())
                .limit(1)
            )

            row = session.execute(stmt).scalar_one_or_none()

            if not row:
                return None

            # Update the current row with new data
            old_message = ChatMessage.model_validate(row.message)
            old_data = old_message.additional_kwargs
            old_data.update(data)
            row.message = ChatMessage(content=old_message.content, role=old_message.role, additional_kwargs=old_data).model_dump()

            session.commit()
            return old_message

    async def aupdate_last_message(self, client_guid: uuid.UUID, user_guid: uuid.UUID, **data: Any) -> Optional[ChatMessage]:
        """Async version of update_last_message, preserving the old message."""
        async with self._async_session() as session:
            # Get the last message by timestamp
            stmt = (
                select(self._table_class)
                .where(
                    self._table_class.client_guid == client_guid,
                    self._table_class.user_guid == user_guid,
                )
                .order_by(self._table_class.timestamp.desc())
                .limit(1)
            )

            result = await session.execute(stmt)
            row = result.scalar_one_or_none()

            if not row:
                return None

            # Update the current row with new data
            old_message = ChatMessage.model_validate(row.message)
            old_data = old_message.additional_kwargs
            old_data.update(data)
            row.message = ChatMessage(content=old_message.content, role=old_message.role, additional_kwargs=old_data).model_dump()

            await session.commit()
            return old_message
