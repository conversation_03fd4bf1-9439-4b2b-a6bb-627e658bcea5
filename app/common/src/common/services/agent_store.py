"""SQLAlchemy store for agent workflows."""

from __future__ import annotations

import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional, Type
from urllib.parse import urlparse

from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.schema import BaseComponent
from sqlalchemy import (
    Column,
    Index,
    UniqueConstraint,
    create_engine,
    select,
    text,
)
from sqlalchemy.dialects.postgresql import JSONB, TEXT, TIMESTAMP, UUID
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from common.settings import settings
from common.utils import get_current_time


@dataclass
class AgentEntry:
    """Dataclass representation of an agent workflow."""

    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    name: str
    definition: dict[str, Any]


def get_data_model(base: Type[Any], table_name: str, schema_name: str) -> Type[Any]:
    """Create dynamic SQLAlchemy model for agent workflows."""

    class AbstractData(base):  # pylint: disable=too-few-public-methods
        """Abstract base class for agent data model."""

        __abstract__ = True
        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        created_at = Column(TIMESTAMP, nullable=False)
        updated_at = Column(TIMESTAMP, nullable=False)
        name = Column(TEXT, nullable=False)
        definition = Column(JSONB, nullable=False)

    return type(
        f"Data{table_name}",
        (AbstractData,),
        {
            "__tablename__": table_name,
            "__table_args__": (
                UniqueConstraint("id", name=f"{table_name}:unique_id"),
                Index(f"{table_name}:idx_name", "name"),
                {"schema": schema_name},
            ),
        },
    )


class AgentStore(BaseComponent):
    """Store for agent workflow definitions."""

    table_name: str = Field(default="agents", description="Postgres table name.")
    schema_name: str = Field(default="public", description="Postgres schema name.")

    _table_class: Any = PrivateAttr()
    _session: sessionmaker[Any] = PrivateAttr()
    _async_session: sessionmaker[Any] = PrivateAttr()

    def __init__(
        self,
        session: sessionmaker[Any],
        async_session: sessionmaker[Any],
        table_name: str = "agents",
        schema_name: str = "public",
    ) -> None:
        super().__init__(table_name=table_name.lower(), schema_name=schema_name.lower())
        base = declarative_base()
        self._table_class = get_data_model(base, table_name, schema_name)
        self._session = session
        self._async_session = async_session
        self._initialize(base)

    @classmethod
    def from_params(
        cls,
        host: Optional[str] = None,
        port: Optional[str] = None,
        database: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        table_name: str = "agents",
        schema_name: str = "public",
        connection_string: Optional[str] = None,
        async_connection_string: Optional[str] = None,
        debug: bool = False,
    ) -> "AgentStore":
        """Create an AgentStore instance from connection parameters."""
        conn_str = connection_string or f"postgresql+psycopg://{user}:{password}@{host}:{port}/{database}"
        async_conn_str = async_connection_string or (f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}")
        session, async_session = cls._connect(conn_str, async_conn_str, debug)
        return cls(
            session=session,
            async_session=async_session,
            table_name=table_name,
            schema_name=schema_name,
        )

    @classmethod
    def from_uri(
        cls,
        uri: str,
        db_name: str,
        table_name: str = "agents",
        schema_name: str = "public",
        debug: bool = False,
    ) -> "AgentStore":
        """Create an AgentStore instance from a database URI."""
        result = urlparse(uri)
        params = {
            "user": result.username,
            "password": result.password,
            "host": result.hostname,
            "port": result.port if result.port else 5432,
        }
        return cls.from_params(
            **params,
            database=db_name,
            table_name=table_name,
            schema_name=schema_name,
            debug=debug,
        )

    @classmethod
    def _connect(cls, connection_string: str, async_connection_string: str, debug: bool) -> tuple[sessionmaker[Any], sessionmaker[Any]]:
        """Create SQLAlchemy session and async session."""
        engine = create_engine(connection_string, echo=debug)
        session = sessionmaker(engine)

        async_engine = create_async_engine(async_connection_string)
        async_session = sessionmaker(async_engine, class_=AsyncSession)
        return session, async_session

    def _create_schema_if_not_exists(self) -> None:
        """Create schema if it does not exist."""
        with self._session() as session, session.begin():
            check_stmt = text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{self.schema_name}'")
            result = session.execute(check_stmt).fetchone()
            if not result:
                create_stmt = text(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name}")
                session.execute(create_stmt)
            session.commit()

    def _create_tables_if_not_exists(self, base: Type[Any]) -> None:
        """Create tables if they do not exist."""
        with self._session() as session, session.begin():
            base.metadata.create_all(session.connection())

    def _initialize(self, base: Type[Any]) -> None:
        """Initialize the AgentStore by creating schema and tables."""
        self._create_schema_if_not_exists()
        self._create_tables_if_not_exists(base)

    def create_agent(self, name: str, definition: dict[str, Any]) -> uuid.UUID:
        """Create a new agent workflow and return its identifier."""
        with self._session() as session:
            now = get_current_time()
            entry = self._table_class(
                name=name,
                definition=definition,
                created_at=now,
                updated_at=now,
            )
            session.add(entry)
            session.commit()
            return entry.id

    async def async_create_agent(self, name: str, definition: dict[str, Any]) -> uuid.UUID:
        """Asynchronously create a new agent workflow and return its identifier."""
        async with self._async_session() as session:
            now = get_current_time()
            entry = self._table_class(
                name=name,
                definition=definition,
                created_at=now,
                updated_at=now,
            )
            session.add(entry)
            await session.commit()
            return entry.id

    def get_agent(self, agent_id: uuid.UUID) -> AgentEntry:
        """Retrieve an agent workflow by its identifier."""
        with self._session() as session:
            row = session.query(self._table_class).filter_by(id=agent_id).first()
            if not row:
                raise LookupError("Agent not found")
            return AgentEntry(
                id=row.id,
                name=row.name,
                definition=row.definition,
                created_at=row.created_at,
                updated_at=row.updated_at,
            )

    async def async_get_agent(self, agent_id: uuid.UUID) -> AgentEntry:
        """Asynchronously retrieve an agent workflow by its identifier."""
        async with self._async_session() as session:
            query = select(self._table_class).where(self._table_class.id == agent_id)
            result = await session.execute(query)
            row = result.scalar_one_or_none()
            if not row:
                raise LookupError("Agent not found")
            return AgentEntry(
                id=row.id,
                name=row.name,
                definition=row.definition,
                created_at=row.created_at,
                updated_at=row.updated_at,
            )

    @classmethod
    def get_instance(cls) -> "AgentStore":
        """Get a singleton instance of AgentStore."""
        return cls.from_uri(
            uri=settings.database.connection_string,
            db_name=settings.database.db_name,
            table_name="agents",
        )
