"""Module for batch job management in PostgreSQL databases.

This module provides functionality for tracking and managing batch jobs
with PostgreSQL, supporting both synchronous and asynchronous operations.
"""

from __future__ import annotations

import uuid
from dataclasses import dataclass
from datetime import datetime
from enum import IntEnum
from typing import Any, Optional, Type
from urllib.parse import urlparse

# TODO: do we really need to import them from llama_index?
from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.schema import BaseComponent
from sqlalchemy import (
    Column,
    Index,
    Integer,
    UniqueConstraint,
    create_engine,
    make_url,
    select,
    text,
)
from sqlalchemy.dialects.postgresql import JSON, JSONB, TEXT, TIMESTAMP, UUID
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from common.settings import settings
from common.utils import get_current_time


class BatchJobStatusEnum(IntEnum):
    """Enum for batch job status values."""

    PENDING = 0
    PROCESSING = 1
    COMPLETED = 2
    FAILED = 3
    PARTIALLY_COMPLETED = 4


@dataclass(kw_only=False)
class Job:  # pylint: disable=too-many-instance-attributes
    """Dataclass representation of a batch job."""

    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    client_guid: uuid.UUID
    status: BatchJobStatusEnum
    status_description: Optional[str]
    processed_count: int
    total_count: int
    job_params: dict[str, Any]
    results: list[dict[str, Any]]


def get_data_model(
    base: Type[Any],
    index_name: str,
    schema_name: str,
    use_jsonb: bool = False,
) -> Type[Any]:
    """Create a dynamic SQLAlchemy model for batch job management.

    Args:
        base: SQLAlchemy declarative base class
        index_name: Name to use for the table
        schema_name: Name of the database schema
        use_jsonb: Whether to use JSONB or JSON for data columns

    Returns:
        Dynamically created SQLAlchemy model class
    """
    tablename = index_name
    class_name = f"Data{index_name}"

    json_dtype = JSONB if use_jsonb else JSON

    class AbstractData(base):  # pylint: disable=too-few-public-methods
        """Base class for batch job data models."""

        __abstract__ = True
        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        created_at = Column(TIMESTAMP, nullable=False)
        updated_at = Column(TIMESTAMP, nullable=False)
        started_at = Column(TIMESTAMP, nullable=True)
        completed_at = Column(TIMESTAMP, nullable=True)
        client_guid = Column(UUID(as_uuid=True), nullable=True)
        status = Column(Integer, nullable=False)
        status_description = Column(TEXT, nullable=True)
        processed_count = Column(Integer, nullable=False)
        total_count = Column(Integer, nullable=False)
        job_params = Column(json_dtype, nullable=False)
        results = Column(json_dtype, nullable=False)

    return type(
        class_name,
        (AbstractData,),
        {
            "__tablename__": tablename,
            "__table_args__": (
                UniqueConstraint("id", name=f"{tablename}:unique_id"),
                Index(f"{tablename}:idx_id", "id"),
                Index(f"{tablename}:idx_status", "status"),
                {"schema": schema_name},
            ),
        },
    )


class JobStore(BaseComponent):
    """Class for tracking batch job management in PostgreSQL.

    This class provides methods for creating and updating batch jobs
    with support for both synchronous and asynchronous operations.
    """

    table_name: str = Field(default="batch_jobs", description="Postgres table name.")
    schema_name: str = Field(default="public", description="Postgres schema name.")

    _table_class: Any = PrivateAttr()
    _session: sessionmaker[Any] = PrivateAttr()
    _async_session: sessionmaker[Any] = PrivateAttr()

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def __init__(
        self,
        session: sessionmaker[Any],
        async_session: sessionmaker[Any],
        table_name: str = "batch_jobs",
        schema_name: str = "public",
        use_jsonb: bool = False,
    ) -> None:
        """Initialize BatchJob with database sessions and table configuration.

        Args:
            session: SQLAlchemy sessionmaker for synchronous operations
            async_session: SQLAlchemy sessionmaker for asynchronous operations
            table_name: Name of the table to use
            schema_name: Name of the schema to use
            use_jsonb: Whether to use JSONB or JSON for data
        """
        super().__init__(
            table_name=table_name.lower(),
            schema_name=schema_name.lower(),
        )
        base = declarative_base()
        self._table_class = get_data_model(
            base,
            table_name,
            schema_name,
            use_jsonb,
        )
        self._session = session
        self._async_session = async_session
        self._initialize(base)

    # pylint: disable=too-many-arguments, too-many-positional-arguments, too-many-locals
    @classmethod
    def from_params(
        cls,
        host: Optional[str] = None,
        port: Optional[str] = None,
        database: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        table_name: str = "batch_jobs",
        schema_name: str = "public",
        connection_string: Optional[str] = None,
        async_connection_string: Optional[str] = None,
        debug: bool = False,
        use_jsonb: bool = False,
    ) -> "JobStore":
        """Return connection string from database parameters."""
        conn_str = connection_string or f"postgresql+psycopg://{user}:{password}@{host}:{port}/{database}"
        async_conn_str = async_connection_string or (f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}")
        session, async_session = cls._connect(conn_str, async_conn_str, debug)
        return cls(
            session=session,
            async_session=async_session,
            table_name=table_name,
            schema_name=schema_name,
            use_jsonb=use_jsonb,
        )

    @classmethod
    def from_uri(
        cls,
        uri: str,
        db_name: str,
        table_name: str = "batch_jobs",
        schema_name: str = "public",
        debug: bool = False,
        use_jsonb: bool = False,
    ) -> "JobStore":
        """Return connection string from database parameters."""
        result = urlparse(uri)
        params: dict[str, Any] = {
            "user": result.username,
            "password": result.password,
            "host": result.hostname,
            "port": result.port if result.port else 5432,
        }
        return cls.from_params(
            **params,
            database=db_name,
            table_name=table_name,
            schema_name=schema_name,
            debug=debug,
            use_jsonb=use_jsonb,
        )

    @classmethod
    def _connect(cls, connection_string: str, async_connection_string: str, debug: bool) -> tuple[sessionmaker[Any], sessionmaker[Any]]:
        _engine = create_engine(connection_string, echo=debug)
        session = sessionmaker(_engine)

        _async_engine = create_async_engine(async_connection_string)
        async_session = sessionmaker(_async_engine, class_=AsyncSession)
        return session, async_session

    def _create_schema_if_not_exists(self) -> None:
        with self._session() as session, session.begin():
            # Check if the specified schema exists with "CREATE" statement
            check_schema_statement = text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{self.schema_name}'")
            result = session.execute(check_schema_statement).fetchone()

            # If the schema does not exist, then create it
            if not result:
                create_schema_statement = text(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name}")
                session.execute(create_schema_statement)

            session.commit()

    def _create_tables_if_not_exists(self, base: Type[Any]) -> None:
        with self._session() as session, session.begin():
            base.metadata.create_all(session.connection())

    def _initialize(self, base: Type[Any]) -> None:
        """Initialize the database schema and tables.

        Args:
            base: SQLAlchemy declarative base
        """
        self._create_schema_if_not_exists()
        self._create_tables_if_not_exists(base)

    @classmethod
    def from_urixx(
        cls,
        uri: str,
        db_name: str,
        table_name: str = "batch_jobs",
        schema_name: str = "public",
        debug: bool = False,
    ) -> JobStore:
        """Create a BatchJob instance from a database URI.

        Args:
            uri: PostgreSQL connection URI
            db_name: Database name
            table_name: Name of the table to use
            schema_name: Name of the schema to use
            debug: Whether to enable SQLAlchemy echo mode

        Returns:
            Configured BatchJob instance
        """
        parsed_url = make_url(uri)
        cleaned_uri = f"{parsed_url.username}:{parsed_url.password}@{parsed_url.host}:{parsed_url.port}/{db_name}"
        connection_string = f"postgresql+psycopg://{cleaned_uri}"
        async_connection_string = f"postgresql+asyncpg://{cleaned_uri}"

        engine = create_engine(connection_string, echo=debug)
        session = sessionmaker(engine)

        async_engine = create_async_engine(async_connection_string)
        async_session = sessionmaker(async_engine, class_=AsyncSession)

        return cls(
            session=session,
            async_session=async_session,
            table_name=table_name,
            schema_name=schema_name,
        )

    def _prepare_new_job(
        self,
        client_guid: Optional[uuid.UUID],
        total_count: int,
        job_params: Optional[dict[str, Any]] = None,
        status: int = BatchJobStatusEnum.PENDING,
    ) -> Any:
        """Prepare a new job instance with common parameters.

        Args:
            client_guid: Client identifier
            job_params: Generation parameters and configuration
            status: Initial job status

        Returns:
            New job instance
        """
        now = get_current_time()
        return self._table_class(
            created_at=now,
            updated_at=now,
            started_at=None,
            completed_at=None,
            client_guid=client_guid,
            processed_count=0,
            total_count=total_count,
            status=status,
            job_params=job_params,
            results={},
        )

    def create_job(
        self,
        client_guid: Optional[uuid.UUID],
        total_count: int,
        job_params: Optional[dict[str, Any]],
        status: int = BatchJobStatusEnum.PENDING,
    ) -> uuid.UUID:
        """Create a new batch job synchronously.

        Args:
            client_guid: Client identifier
            total_count: Total count of tasks in the job
            job_params: Generation parameters and configuration
            status: Initial job status

        Returns:
            UUID of the created batch job
        """
        with self._session() as session:
            new_job = self._prepare_new_job(client_guid, total_count, job_params, status)
            session.add(new_job)
            session.commit()
            return new_job.id

    async def async_create_job(
        self,
        client_guid: Optional[uuid.UUID],
        total_count: int,
        job_params: Optional[dict[str, Any]] = None,
        status: int = BatchJobStatusEnum.PENDING,
    ) -> uuid.UUID:
        """Create a new batch job asynchronously.

        Args:
            client_guid: Client identifier
            total_count: Total number of tasks in the job
            job_params: Generation parameters and configuration
            status: Initial job status

        Returns:
            UUID of the created batch job
        """
        async with self._async_session() as session:
            new_job = self._prepare_new_job(client_guid, total_count, job_params, status)
            session.add(new_job)
            await session.commit()
            return new_job.id

    def _update_job_data(self, session: Any, job: Job) -> None:
        """Update job data fields with common logic.

        Args:
            job: Job instance to update
        """
        j = session.query(self._table_class).filter_by(id=job.id).first()

        j.id = (job.id,)
        j.client_guid = (job.client_guid,)
        j.status = (job.status,)
        j.status_description = (job.status_description if job.status != BatchJobStatusEnum.COMPLETED else None,)
        j.processed_count = (job.processed_count,)
        j.total_count = (job.total_count,)
        j.job_params = (job.job_params,)
        j.results = (job.results,)
        j.created_at = (job.created_at,)
        j.updated_at = (get_current_time(),)
        j.started_at = (job.started_at,)
        j.completed_at = (job.completed_at,)

    def update_job(self, job: Job) -> bool:
        """Update an existing batch job synchronously."""
        with self._session() as session:
            self._update_job_data(session=session, job=job)
            session.commit()
            return True

    async def async_update_job(self, job: Job) -> bool:
        """Update an existing batch job asynchronously."""
        async with self._async_session() as session:
            self._update_job_data(session=session, job=job)
            await session.commit()
            return True

    def get_job(self, job_id: uuid.UUID) -> Job:
        """Retrieve a batch job by ID synchronously.

        Args:
            job_id: UUID of the job to retrieve

        Returns:
            Job data as a dictionary or None if not found
        """
        with self._session() as session:
            job = session.query(self._table_class).filter_by(id=job_id).first()

            if not job:
                raise LookupError("Job not found")

            return Job(
                id=job.id,
                client_guid=job.client_guid,
                status=job.status,
                status_description=job.status_description,
                processed_count=job.processed_count,
                total_count=job.total_count,
                job_params=job.job_params,
                results=job.results,
                created_at=job.created_at,
                updated_at=job.updated_at,
                started_at=job.started_at,
                completed_at=job.completed_at,
            )

    async def async_get_job(self, job_id: uuid.UUID) -> Job:
        """Retrieve a batch job by ID asynchronously.

        Args:
            job_id: UUID of the job to retrieve

        Returns:
            Job data as a dictionary or None if not found
        """
        async with self._async_session() as session:
            query = select(self._table_class).where(self._table_class.id == job_id)
            query_result = await session.execute(query)
            job = query_result.scalar_one_or_none()

            if not job:
                raise LookupError("Job not found")

            return Job(
                id=job.id,
                client_guid=job.client_guid,
                status=job.status,
                status_description=job.status_description,
                processed_count=job.processed_count,
                total_count=job.total_count,
                job_params=job.job_params,
                results=job.results,
                created_at=job.created_at,
                updated_at=job.updated_at,
                started_at=job.started_at,
                completed_at=job.completed_at,
            )

    @classmethod
    def get_instance(cls) -> JobStore:
        """Get a configured BatchJob manager instance.

        Returns:
            BatchJob: Configured BatchJob instance
        """
        return cls.from_uri(
            uri=settings.database.connection_string,
            db_name=settings.database.db_name,
            table_name="batch_jobs",
        )
