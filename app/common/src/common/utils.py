"""Utility functions."""

import os
from datetime import datetime, timezone
from typing import TYPE_CHECKING, Optional

import boto3
from botocore.exceptions import ClientError

if TYPE_CHECKING:
    from mypy_boto3_secretsmanager import SecretsManagerClient


def get_current_time() -> datetime:
    """
    Returns the current time in UTC without timezone information.
    """
    return datetime.now(timezone.utc).replace(tzinfo=None)


def store_in_secrets_manager(secret_name: str, secret_value: str, description: Optional[str] = ""):
    """Store a secret in AWS Secrets Manager."""
    client: SecretsManagerClient = boto3.client(  # type: ignore
        "secretsmanager", region_name=os.getenv("AWS_REGION", "us-east-1")
    )
    try:
        client.create_secret(Name=secret_name, SecretString=secret_value, Description=description)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ResourceExistsException":  # type: ignore
            client.put_secret_value(SecretId=secret_name, SecretString=secret_value)
        else:
            raise
