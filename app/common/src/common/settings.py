"""
This module contains general settings for all services in the application.
"""

import logging
import os

import boto3
from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseModel):
    """
    Database settings
    """

    connection_string: str = Field(default="")
    db_name: str = Field(default="encore")


class LlmGatewaySettings(BaseModel):
    """
    LLM Gateway settings
    """

    base_url: str = Field(default=os.getenv("LLM_GATEWAY_URL", "http://llm_gateway:8008"))
    master_key: str = Field(default="#{LitellmMasterKey}")


class OTLPSettings(BaseModel):
    """
    otlp settings
    """

    otlp_headers: str = Field(default=os.getenv("OTEL_EXPORTER_OTLP_HEADERS", "api-key=xyz"))
    otlp_endpoint: str = Field(default=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT", "https://otlp.nr-data.net:4317"))
    otlp_protocol: str = Field(default=os.getenv("OTEL_EXPORTER_OTLP_PROTOCOL", "grpc"))


class LlmSettings(BaseModel):
    """
    Llm settings
    """

    embed_dim: int = Field(default=1536)
    embed_model: str = Field(default="text-embedding-3-small")
    rag_model: str = Field(default="aws:anthropic.claude-3-haiku-20240307-v1:0")  # exists in US, EU, and AP regions
    top_k: int = Field(default=10)
    default_model: str = Field(default="aws:anthropic.claude-3-5-sonnet-20240620-v1:0")  # exists in US, EU, and AP regions
    default_temperature: float = Field(default=0.1)
    default_top_p: float = Field(default=0.9)
    default_max_tokens: int = Field(default=4096)


class TaskProcesssorSettings(BaseModel):
    """
    Task Processor settings
    """

    task_queue_namespace: str = Field(default="encore-task-processor")
    task_max_retries: int = Field(default=3)
    task_time_limit_ms: int = Field(default=180000)  # 3 minutes
    task_min_backoff_ms: int = Field(default=180000)  # 3 minutes
    task_max_backoff_ms: int = Field(default=3600000)  # 1 hour
    task_visibility_timeout: int = Field(default=600)  # 10 minutes
    polling_interval_seconds: int = Field(default=5)  # seconds
    batch_size: int = Field(default=10)  # number of tasks to process in a batch
    bucket_name: str = Field(default="aca-dev-encore-us-east-1")  # s3 bucket
    callback_timeout: int = Field(default=10)  # seconds
    store_job_params: bool = False


class CorsSettings(BaseModel):
    """
    Cors settings
    """

    origins: str = Field(default="")


class AzureSettings(BaseModel):
    """
    Azure settings
    """

    api_key: str = Field(default="")
    base_url: str = Field(default="")
    api_version: str = Field(default="")


class AppSettings(BaseSettings):
    """
    App settings
    """

    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    llm: LlmSettings = Field(default_factory=LlmSettings)
    llm_gateway: LlmGatewaySettings = Field(default_factory=LlmGatewaySettings)
    otlp: OTLPSettings = Field(default_factory=OTLPSettings)
    cors: CorsSettings = Field(default_factory=CorsSettings)
    task_processor: TaskProcesssorSettings = Field(default_factory=TaskProcesssorSettings)

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter=".",
        extra="ignore",
    )

    @classmethod
    def get_settings(cls) -> "AppSettings":
        """
        Get settings based on the environment
        """
        is_local = os.getenv("is_local")  # TODO: get rid of this
        if is_local:
            settings_cls = cls()
        else:
            env_name = os.getenv("env_name")
            settings_cls = cls.from_aws_parameter_store(env_name)
        return settings_cls

    @classmethod
    def _fetch_params(cls, ssm, value):
        if isinstance(value, dict):
            section = {}
            for key, path in value.items():
                section[key] = cls._fetch_params(ssm, path)

            return section

        try:
            response = ssm.get_parameter(Name=value, WithDecryption=True)
            return response["Parameter"]["Value"]
        except ssm.exceptions.ParameterNotFound:
            logging.error("Parameter not found: %s", value)
            return None

    @classmethod
    def from_aws_parameter_store(cls, env_name: str) -> "AppSettings":
        """
        Fetch settings from AWS Parameter Store
        """
        ssm = boto3.client("ssm")
        settings_dict = {
            "database": {},
            "llm": {},
            "llm_gateway": {},
            "cors": {},
        }

        encore_root = f"/alpha/{env_name}/encore"
        global_root = f"/alpha/{env_name}/global/Web"

        param_mapping = {
            "database": {
                "connection_string": f"{encore_root}/database/ConnectionString",
            },
            "llm": {
                "default_model": f"{encore_root}/llm/DefaultModel",
            },
            "llm_gateway": {
                "master_key": f"{encore_root}/llm/LitellmMasterKey",
                "base_url": f"{encore_root}/llm/LlmGatewayBaseUrl",
            },
            "otlp": {
                "otlp_headers": f"{encore_root}/otlp/OTEL_EXPORTER_OTLP_HEADERS",
                "otlp_endpoint": f"{encore_root}/otlp/OTEL_EXPORTER_OTLP_ENDPOINT",
                "otlp_protocol": f"{encore_root}/otlp/OTEL_EXPORTER_OTLP_PROTOCOL",
            },
            "cors": {
                "origins": f"{global_root}/CorsOrigins",
            },
            "task_processor": {
                "bucket_name": f"{encore_root}/s3/BucketName",
            },
        }

        # Fetch all parameters from AWS Parameter Store
        for section, params in param_mapping.items():
            settings_dict[section] = cls._fetch_params(ssm, params)

        return cls(**settings_dict)


# Usage examples:
settings = AppSettings.get_settings()
