"""
Logging configuration module for the Encore application.

This module provides a standardized logging setup across the application
with the following features:
- Configures logging format with timestamp, logger name, level, and message
- Sets logging level based on environment (DEBUG for dev, INFO for others)
- Provides a LoggingSetup class for creating named loggers

Usage:
    from common.logging import LoggingSetup
    logger = LoggingSetup.setup_logger(__name__)
"""

import logging
import os

FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
if os.getenv("env_name") == "dev":
    logging.basicConfig(level=logging.DEBUG, format=FORMAT)
else:
    logging.basicConfig(level=logging.INFO, format=FORMAT)


class LoggingSetup:
    """
    A class to setup logging levels
    """

    # pylint: disable=too-few-public-methods
    @staticmethod
    def setup_logger(logging_name: str):
        """setup logger"""
        return logging.getLogger(logging_name)
