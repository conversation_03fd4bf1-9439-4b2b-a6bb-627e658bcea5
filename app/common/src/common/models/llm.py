"""This module provides Pydantic models for text generation requests."""

from typing import Dict, List, Optional, TypeAlias

import litellm.proxy._types
import litellm.types.completion
import litellm.types.embedding
from pydantic import (  # pylint: disable=no-name-in-module
    BaseModel,
    ConfigDict,
    Field,
)

TokenCountRequest: TypeAlias = litellm.proxy._types.TokenCountRequest
EmbeddingRequest: TypeAlias = litellm.types.embedding.EmbeddingRequest


class CompletionRequest(litellm.types.completion.CompletionRequest):
    """Request model for text completion."""

    files: Optional[List[str]] = Field(None, description="List of S3 presigned URLs or ARNs to use for the completion request.")


class ChatCompletionRequest(BaseModel):
    """Request model for chat completion."""

    model: str
    messages: List[Dict[str, str]]
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    n: Optional[int] = None
    stream: Optional[bool] = None
    stop: Optional[List[str]] = None
    max_tokens: Optional[int] = None
    presence_penalty: Optional[float] = None
    frequency_penalty: Optional[float] = None
    logit_bias: Optional[Dict[str, float]] = None
    user: Optional[str] = None
    response_format: Optional[Dict[str, str]] = None
    seed: Optional[int] = None
    tools: Optional[List[str]] = None
    tool_choice: Optional[str] = None

    files: Optional[List[str]] = Field(None, description="List of S3 presigned URLs or ARNs to use for the completion request.")

    model_config = ConfigDict(protected_namespaces=(), extra="allow")
