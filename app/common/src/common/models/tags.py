from datetime import datetime
from typing import List
from uuid import UUID

from pydantic import BaseModel, Field
from sqlalchemy import Column, TIMESTAMP, TEXT, INTEGER, Numeric
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import declarative_base

# SQLAlchemy setup for direct database access
Base = declarative_base()


class LiteLLMDailyTagSpend(Base):
    """SQLAlchemy model for LiteLLM_DailyTagSpend table."""
    __tablename__ = "LiteLLM_DailyTagSpend"
    __table_args__ = {"schema": "public"}

    id = Column(PG_UUID(as_uuid=True), primary_key=True)
    tag = Column(TEXT)
    date = Column(TEXT)
    api_key = Column(TEXT)
    model = Column(TEXT)
    model_group = Column(TEXT)
    custom_llm_provider = Column(TEXT)
    prompt_tokens = Column(INTEGER)
    completion_tokens = Column(INTEGER)
    cache_read_input_tokens = Column(INTEGER)
    cache_creation_input_tokens = Column(INTEGER)
    api_requests = Column(INTEGER)
    successful_requests = Column(INTEGER)
    failed_requests = Column(INTEGER)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP)
    spend = Column(Numeric) # type: ignore


# Pydantic models for API responses
class TagUsageSummary(BaseModel):
    """Summary of tag usage for a specific tag."""
    tag: str
    total_spend: float = Field(..., description="Total spend in USD")
    total_requests: int = Field(..., description="Total API requests")
    total_tokens: int = Field(..., description="Total tokens (prompt + completion)")
    success_rate: float = Field(..., description="Success rate as percentage")
    date_range: str = Field(..., description="Date range of the data")


class TagUsageDetail(BaseModel):
    """Detailed tag usage information."""
    id: UUID
    tag: str
    date: str
    api_key: str
    model: str
    model_group: str
    custom_llm_provider: str
    prompt_tokens: int
    completion_tokens: int
    cache_read_input_tokens: int
    cache_creation_input_tokens: int
    spend: float
    api_requests: int
    successful_requests: int
    failed_requests: int
    created_at: datetime
    updated_at: datetime


class TagUsageByDate(BaseModel):
    """Tag usage aggregated by date."""
    date: str
    spend: float
    api_requests: int
    total_tokens: int
    success_rate: float


class TagUsageByModel(BaseModel):
    """Tag usage aggregated by model."""
    model: str
    model_group: str
    custom_llm_provider: str
    total_spend: float
    total_requests: int
    total_tokens: int
    success_rate: float


class TagUsageResponse(BaseModel):
    """Response model for tag usage endpoints."""
    tag: str
    summary: TagUsageSummary
    daily_data: List[TagUsageByDate]
    model_breakdown: List[TagUsageByModel]
    total_records: int
