"""Response model definitions for the API."""

import uuid
from typing import Optional

from pydantic import BaseModel, Field


class JobResponse(BaseModel):
    """Response model for batch job submission."""

    job_id: uuid.UUID = Field(..., description="UUID of the batch job")

    class Config:  # pylint: disable=too-few-public-methods
        """Configuration class for JobResponse model."""

        json_encoders = {uuid.UUID: str}


class JobStatusResponse(BaseModel):  # pylint: disable=too-many-instance-attributes
    """Response model for batch job status."""

    job_id: str = Field(..., description="UUID of the batch job")
    status: str = Field(..., description="Status of the batch job")
    status_description: Optional[str] = Field(default=None, description="Detailed status description of the batch job")
    progress: Optional[dict] = Field(default_factory=dict, description="Progress information for the batch job")
    created_at: Optional[str] = Field(default=None, description="Timestamp when the batch job was created")
    updated_at: Optional[str] = Field(default=None, description="Timestamp when the batch job was last updated")
    started_at: Optional[str] = Field(default=None, description="Timestamp when the batch job started")
    completed_at: Optional[str] = Field(default=None, description="Timestamp when the batch job completed")
    s3_prefix: Optional[str] = Field(default=None, description="S3 prefix where results are stored")

    class Config:  # pylint: disable=too-few-public-methods
        """Configuration class for JobResponse model."""

        json_encoders = {uuid.UUID: str}


class JobResultResponse(BaseModel):
    """Response model for batch job result."""

    job_id: str = Field(..., description="UUID of the batch job")
    status: str = Field(..., description="Status of the batch job")
    status_description: Optional[str] = Field(default=None, description="Detailed status description of the batch job")
    result_urls: Optional[list] = Field(default_factory=list, description="Result URLs for the batch job")
    pagination: Optional[dict] = Field(default_factory=dict, description="Pagination information for the batch job results")

    class Config:  # pylint: disable=too-few-public-methods
        """Configuration class for JobResponse model."""

        json_encoders = {uuid.UUID: str}
