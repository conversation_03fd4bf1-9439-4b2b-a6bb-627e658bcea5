"""Common utility functions for parsing data."""

import json
import re

from common.logging import LoggingSetup

log = LoggingSetup.setup_logger("parser")


def extract_and_parse_json(content: str):
    """
    Extract and parse JSON content from a string.

    Args:
        content (str): The string to parse

    Returns:
        dict: The parsed JSON content
    """
    try:
        # First, look for markdown code blocks
        markdown_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
        markdown_match = re.search(markdown_pattern, content)
        if markdown_match:
            json_content = markdown_match.group(1).strip()
            return json.loads(json_content, strict=False)

        # handle if header text is present
        # e.g. "here is the json you are looking for\n{...}"
        header_pattern = r".*?(\[.*\]|\{.*\}).*"
        header_match = re.match(header_pattern, content, re.DOTALL)
        if header_match:
            json_content = header_match.group(1).strip()
            return json.loads(json_content, strict=False)

        # Finally, try parsing the entire content
        return json.loads(content.strip(), strict=False)

    except json.decoder.JSONDecodeError as e:
        log.error("JSON parsing error: %s", e)
        raise e
    except Exception as e:
        log.error("Unexpected error while parsing output: %s", e)
        raise e
