"""Tests for PromptStore service."""

import uuid
from typing import Any
from unittest.mock import patch

import pytest

from common.services import PromptStore
from common.services.prompt_store import PromptEntry


class TestPromptStoreCreation:
    """Test PromptStore creation and initialization."""

    def test_prompt_store_initialization(self, prompt_store: PromptStore):
        """Test PromptStore initializes correctly."""
        assert prompt_store.table_name == "test_prompts"
        assert prompt_store.schema_name == "public"
        # Test public interface rather than private attributes
        assert hasattr(prompt_store, '_table_class')
        assert hasattr(prompt_store, '_session')
        assert hasattr(prompt_store, '_async_session')

    def test_from_params_creation(self):
        """Test PromptStore creation from parameters."""
        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_prompts",
                schema_name="custom_schema",
                debug=True
            )

            assert store.table_name == "custom_prompts"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test PromptStore creation from URI."""
        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_prompts",
                debug=True
            )

            assert store.table_name == "uri_prompts"
            assert store.schema_name == "public"

    @patch('common.services.prompt_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.prompt_store.create_engine'), \
                patch('common.services.prompt_store.create_async_engine'), \
                patch('common.services.prompt_store.sessionmaker'):

            store = PromptStore.get_instance()
            assert store.table_name == "prompts"


class TestPromptStoreBasicOperations:
    """Test basic CRUD operations."""

    def test_create_prompt_sync(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test synchronous prompt creation."""
        prompt_id = prompt_store.create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        assert isinstance(prompt_id, int)
        assert prompt_id > 0

        # Verify prompt was created
        prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert prompt.client_guid == sample_prompt_data["client_guid"]
        assert prompt.prompt == sample_prompt_data["prompt"]
        assert prompt.created_at is not None
        assert prompt.updated_at is not None

    @pytest.mark.asyncio
    async def test_create_prompt_async(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test asynchronous prompt creation."""
        prompt_id = await prompt_store.async_create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        assert isinstance(prompt_id, int)
        assert prompt_id > 0

        # Verify prompt was created
        prompt = await prompt_store.async_get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert prompt.client_guid == sample_prompt_data["client_guid"]
        assert prompt.prompt == sample_prompt_data["prompt"]

    def test_get_prompt_sync(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test synchronous prompt retrieval."""
        # Create prompt first
        prompt_id = prompt_store.create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Retrieve prompt
        prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])

        assert isinstance(prompt, PromptEntry)
        assert prompt.id == prompt_id
        assert prompt.client_guid == sample_prompt_data["client_guid"]
        assert prompt.prompt == sample_prompt_data["prompt"]

    @pytest.mark.asyncio
    async def test_get_prompt_async(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test asynchronous prompt retrieval."""
        # Create prompt first
        prompt_id = await prompt_store.async_create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Retrieve prompt
        prompt = await prompt_store.async_get_prompt(prompt_id, sample_prompt_data["client_guid"])

        assert isinstance(prompt, PromptEntry)
        assert prompt.id == prompt_id
        assert prompt.client_guid == sample_prompt_data["client_guid"]
        assert prompt.prompt == sample_prompt_data["prompt"]

    def test_get_nonexistent_prompt_sync(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test retrieving non-existent prompt synchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            prompt_store.get_prompt(fake_id, test_uuids["client_1"])

    @pytest.mark.asyncio
    async def test_get_nonexistent_prompt_async(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test retrieving non-existent prompt asynchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            await prompt_store.async_get_prompt(fake_id, test_uuids["client_1"])

    def test_list_prompts_sync(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test listing prompts synchronously."""
        client_guid = test_uuids["client_1"]

        # Create multiple prompts
        prompts_data = [
            "You are a helpful assistant.",
            "You are a coding expert.",
            "You are a creative writer."
        ]

        created_ids = []
        for prompt_text in prompts_data:
            prompt_id = prompt_store.create_prompt(client_guid, prompt_text)
            created_ids.append(prompt_id)

        # List prompts
        prompts = prompt_store.list_prompts(client_guid)

        assert len(prompts) == len(prompts_data)

        # Verify order (should be by ID)
        for i, prompt in enumerate(prompts):
            assert prompt.id == created_ids[i]
            assert prompt.prompt == prompts_data[i]
            assert prompt.client_guid == client_guid

    @pytest.mark.asyncio
    async def test_list_prompts_async(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test listing prompts asynchronously."""
        client_guid = test_uuids["client_1"]

        # Create multiple prompts
        prompts_data = [
            "Async prompt 1",
            "Async prompt 2",
            "Async prompt 3"
        ]

        created_ids = []
        for prompt_text in prompts_data:
            prompt_id = await prompt_store.async_create_prompt(client_guid, prompt_text)
            created_ids.append(prompt_id)

        # List prompts
        prompts = await prompt_store.async_list_prompts(client_guid)

        assert len(prompts) == len(prompts_data)

        # Verify content
        for i, prompt in enumerate(prompts):
            assert prompt.id == created_ids[i]
            assert prompt.prompt == prompts_data[i]


class TestPromptStoreUpdateDelete:
    """Test update and delete operations."""

    def test_update_prompt_sync(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test synchronous prompt update."""
        # Create prompt
        prompt_id = prompt_store.create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Update prompt
        new_prompt_text = "Updated: " + sample_prompt_data["prompt"]
        prompt_store.update_prompt(
            prompt_id=prompt_id,
            client_guid=sample_prompt_data["client_guid"],
            prompt=new_prompt_text
        )

        # Verify update
        updated_prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert updated_prompt.prompt == new_prompt_text
        assert updated_prompt.updated_at > updated_prompt.created_at

    @pytest.mark.asyncio
    async def test_update_prompt_async(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test asynchronous prompt update."""
        # Create prompt
        prompt_id = await prompt_store.async_create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Update prompt
        new_prompt_text = "Async Updated: " + sample_prompt_data["prompt"]
        await prompt_store.async_update_prompt(
            prompt_id=prompt_id,
            client_guid=sample_prompt_data["client_guid"],
            prompt=new_prompt_text
        )

        # Verify update
        updated_prompt = await prompt_store.async_get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert updated_prompt.prompt == new_prompt_text
        assert updated_prompt.updated_at > updated_prompt.created_at

    def test_update_nonexistent_prompt_sync(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test updating non-existent prompt synchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            prompt_store.update_prompt(
                prompt_id=fake_id,
                client_guid=test_uuids["client_1"],
                prompt="This should fail"
            )

    @pytest.mark.asyncio
    async def test_update_nonexistent_prompt_async(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test updating non-existent prompt asynchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            await prompt_store.async_update_prompt(
                prompt_id=fake_id,
                client_guid=test_uuids["client_1"],
                prompt="This should fail"
            )

    def test_delete_prompt_sync(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test synchronous prompt deletion."""
        # Create prompt
        prompt_id = prompt_store.create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Verify it exists
        prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert prompt is not None

        # Delete prompt
        prompt_store.delete_prompt(prompt_id, sample_prompt_data["client_guid"])

        # Verify deletion
        with pytest.raises(LookupError, match="Prompt not found"):
            prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])

    @pytest.mark.asyncio
    async def test_delete_prompt_async(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test asynchronous prompt deletion."""
        # Create prompt
        prompt_id = await prompt_store.async_create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        # Verify it exists
        prompt = await prompt_store.async_get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert prompt is not None

        # Delete prompt
        await prompt_store.async_delete_prompt(prompt_id, sample_prompt_data["client_guid"])

        # Verify deletion
        with pytest.raises(LookupError, match="Prompt not found"):
            await prompt_store.async_get_prompt(prompt_id, sample_prompt_data["client_guid"])

    def test_delete_nonexistent_prompt_sync(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test deleting non-existent prompt synchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            prompt_store.delete_prompt(fake_id, test_uuids["client_1"])

    @pytest.mark.asyncio
    async def test_delete_nonexistent_prompt_async(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test deleting non-existent prompt asynchronously."""
        fake_id = 99999

        with pytest.raises(LookupError, match="Prompt not found"):
            await prompt_store.async_delete_prompt(fake_id, test_uuids["client_1"])


class TestPromptStoreClientScoping:
    """Test client isolation and scoping."""

    def test_client_isolation(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test that prompts are properly isolated between clients."""
        client1 = test_uuids["client_1"]
        client2 = test_uuids["client_2"]

        # Create prompts for different clients
        prompt1_id = prompt_store.create_prompt(client1, "Client 1 prompt")
        prompt2_id = prompt_store.create_prompt(client2, "Client 2 prompt")

        # Verify client 1 can only see their prompt
        client1_prompts = prompt_store.list_prompts(client1)
        assert len(client1_prompts) == 1
        assert client1_prompts[0].prompt == "Client 1 prompt"

        # Verify client 2 can only see their prompt
        client2_prompts = prompt_store.list_prompts(client2)
        assert len(client2_prompts) == 1
        assert client2_prompts[0].prompt == "Client 2 prompt"

        # Verify cross-client access is blocked
        with pytest.raises(LookupError):
            prompt_store.get_prompt(prompt1_id, client2)  # Client 2 trying to access Client 1's prompt

        with pytest.raises(LookupError):
            prompt_store.get_prompt(prompt2_id, client1)  # Client 1 trying to access Client 2's prompt

    def test_client_operations_dont_interfere(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test that operations on one client don't affect another."""
        client1 = test_uuids["client_1"]
        client2 = test_uuids["client_2"]

        # Create prompts for both clients
        client1_prompts = []
        client2_prompts = []

        for i in range(3):
            prompt1_id = prompt_store.create_prompt(client1, f"Client 1 prompt {i}")
            prompt2_id = prompt_store.create_prompt(client2, f"Client 2 prompt {i}")
            client1_prompts.append(prompt1_id)
            client2_prompts.append(prompt2_id)

        # Delete all prompts for client 1
        for prompt_id in client1_prompts:
            prompt_store.delete_prompt(prompt_id, client1)

        # Verify client 1 prompts are deleted
        remaining_client1_prompts = prompt_store.list_prompts(client1)
        assert len(remaining_client1_prompts) == 0

        # Verify client 2 prompts are unaffected
        remaining_client2_prompts = prompt_store.list_prompts(client2)
        assert len(remaining_client2_prompts) == 3

    def test_empty_client_prompt_list(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test listing prompts for client with no prompts."""
        client_guid = test_uuids["client_1"]

        # List prompts for client with no prompts
        prompts = prompt_store.list_prompts(client_guid)
        assert len(prompts) == 0
        assert prompts == []


class TestPromptStoreDataIntegrity:
    """Test data integrity and content handling."""

    def test_prompt_timestamps(self, prompt_store: PromptStore, sample_prompt_data: dict[str, Any]):
        """Test that timestamps are properly set and maintained."""
        prompt_id = prompt_store.create_prompt(
            client_guid=sample_prompt_data["client_guid"],
            prompt=sample_prompt_data["prompt"]
        )

        prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])

        # Timestamps should be set
        assert prompt.created_at is not None
        assert prompt.updated_at is not None
        # For new records, created_at and updated_at should be the same
        assert prompt.created_at == prompt.updated_at

        # Update prompt and verify timestamp change
        prompt_store.update_prompt(
            prompt_id=prompt_id,
            client_guid=sample_prompt_data["client_guid"],
            prompt="Updated prompt"
        )

        updated_prompt = prompt_store.get_prompt(prompt_id, sample_prompt_data["client_guid"])
        assert updated_prompt.updated_at > updated_prompt.created_at

    def test_unicode_content_handling(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of unicode content in prompts."""
        client_guid = test_uuids["client_1"]

        unicode_prompts = [
            "你好，我是一个AI助手。请用中文回答问题。",  # Chinese
            "Bonjour! Je suis un assistant IA. Répondez en français s'il vous plaît.",  # French
            "¡Hola! Soy un asistente de IA. Por favor responde en español.",  # Spanish
            "🤖 You are an AI assistant with emoji support! 🌟✨🚀",  # Emojis
            "Math symbols: ∑∞∫∂∇ and special chars: àáâãäåæçèéêë",  # Special characters
        ]

        created_ids = []
        for prompt_text in unicode_prompts:
            prompt_id = prompt_store.create_prompt(client_guid, prompt_text)
            created_ids.append(prompt_id)

        # Retrieve and verify unicode preservation
        for i, prompt_id in enumerate(created_ids):
            prompt = prompt_store.get_prompt(prompt_id, client_guid)
            assert prompt.prompt == unicode_prompts[i]

    def test_large_prompt_content(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of large prompt content."""
        client_guid = test_uuids["client_1"]

        # Create a large prompt (simulating a complex system prompt)
        large_prompt = """
        You are an advanced AI assistant with the following capabilities and guidelines:

        1. EXPERTISE AREAS:
        - Software development and programming in multiple languages
        - Data science and machine learning
        - System architecture and design patterns
        - Database design and optimization
        - Cloud computing and DevOps practices

        2. RESPONSE GUIDELINES:
        - Always provide accurate, well-researched information
        - Include practical examples when relevant
        - Explain complex concepts in simple terms
        - Offer multiple approaches when applicable
        - Consider edge cases and potential issues

        3. CODE STANDARDS:
        - Follow language-specific best practices
        - Include proper error handling
        - Add meaningful comments
        - Use descriptive variable names
        - Consider performance implications

        4. COMMUNICATION STYLE:
        - Be professional yet approachable
        - Ask clarifying questions when needed
        - Provide step-by-step instructions
        - Offer to explain further if needed
        - Acknowledge limitations when appropriate

        Remember to always prioritize user safety and ethical considerations in your responses.
        """ * 10  # Make it even larger

        prompt_id = prompt_store.create_prompt(client_guid, large_prompt)

        # Retrieve and verify
        retrieved_prompt = prompt_store.get_prompt(prompt_id, client_guid)
        assert retrieved_prompt.prompt == large_prompt
        assert len(retrieved_prompt.prompt) > 5000  # Verify it's actually large

    def test_special_characters_in_prompts(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of special characters and edge cases."""
        client_guid = test_uuids["client_1"]

        special_prompts = [
            "",  # Empty prompt
            " ",  # Single space
            "\n\n\n",  # Only newlines
            "Prompt with\ttabs\tand\nnewlines\r\n",  # Mixed whitespace
            "JSON: {\"key\": \"value\", \"number\": 42}",  # JSON content
            "SQL: SELECT * FROM users WHERE name = 'O''Brien';",  # SQL with quotes
            "Regex: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",  # Regex pattern
            "Code: print(\"Hello, World!\")",  # Code snippet
        ]

        for prompt_text in special_prompts:
            prompt_id = prompt_store.create_prompt(client_guid, prompt_text)
            retrieved_prompt = prompt_store.get_prompt(prompt_id, client_guid)
            assert retrieved_prompt.prompt == prompt_text

    def test_prompt_ordering(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test that prompts are returned in correct order."""
        client_guid = test_uuids["client_1"]

        # Create prompts in specific order
        prompt_texts = [f"Prompt {i}" for i in range(5)]
        created_ids = []

        for prompt_text in prompt_texts:
            prompt_id = prompt_store.create_prompt(client_guid, prompt_text)
            created_ids.append(prompt_id)

        # List prompts and verify order
        prompts = prompt_store.list_prompts(client_guid)

        # Should be ordered by ID (creation order)
        for i, prompt in enumerate(prompts):
            assert prompt.id == created_ids[i]
            assert prompt.prompt == prompt_texts[i]

        # Verify IDs are in ascending order
        ids = [prompt.id for prompt in prompts]
        assert ids == sorted(ids)


class TestPromptStoreIntegration:
    """Integration tests for PromptStore."""

    @pytest.mark.asyncio
    async def test_sync_async_consistency(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test that sync and async operations are consistent."""
        client_guid = test_uuids["client_1"]

        # Create prompts using both sync and async methods
        sync_prompt_id = prompt_store.create_prompt(client_guid, "Sync prompt")
        async_prompt_id = await prompt_store.async_create_prompt(client_guid, "Async prompt")

        # Retrieve using both methods
        sync_prompt_sync_get = prompt_store.get_prompt(sync_prompt_id, client_guid)
        sync_prompt_async_get = await prompt_store.async_get_prompt(sync_prompt_id, client_guid)
        async_prompt_sync_get = prompt_store.get_prompt(async_prompt_id, client_guid)
        async_prompt_async_get = await prompt_store.async_get_prompt(async_prompt_id, client_guid)

        # Verify consistency
        assert sync_prompt_sync_get.prompt == sync_prompt_async_get.prompt == "Sync prompt"
        assert async_prompt_sync_get.prompt == async_prompt_async_get.prompt == "Async prompt"

        # List using both methods
        sync_list = prompt_store.list_prompts(client_guid)
        async_list = await prompt_store.async_list_prompts(client_guid)

        assert len(sync_list) == len(async_list) == 2
        sync_prompts = {p.id: p.prompt for p in sync_list}
        async_prompts = {p.id: p.prompt for p in async_list}
        assert sync_prompts == async_prompts

    def test_complete_prompt_lifecycle(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test a complete prompt lifecycle from creation to deletion."""
        client_guid = test_uuids["client_1"]

        # 1. Create prompt
        initial_prompt = "You are a helpful assistant."
        prompt_id = prompt_store.create_prompt(client_guid, initial_prompt)

        # 2. Verify creation
        prompt = prompt_store.get_prompt(prompt_id, client_guid)
        assert prompt.prompt == initial_prompt
        assert prompt.created_at is not None

        # 3. Update prompt
        updated_prompt = "You are a helpful coding assistant."
        prompt_store.update_prompt(prompt_id, client_guid, updated_prompt)

        # 4. Verify update
        prompt = prompt_store.get_prompt(prompt_id, client_guid)
        assert prompt.prompt == updated_prompt
        assert prompt.updated_at > prompt.created_at

        # 5. List prompts
        prompts = prompt_store.list_prompts(client_guid)
        assert len(prompts) == 1
        assert prompts[0].id == prompt_id

        # 6. Delete prompt
        prompt_store.delete_prompt(prompt_id, client_guid)

        # 7. Verify deletion
        with pytest.raises(LookupError):
            prompt_store.get_prompt(prompt_id, client_guid)

        prompts = prompt_store.list_prompts(client_guid)
        assert len(prompts) == 0

    def test_multiple_clients_workflow(self, prompt_store: PromptStore, test_uuids: dict[str, uuid.UUID]):
        """Test realistic workflow with multiple clients."""
        client1 = test_uuids["client_1"]
        client2 = test_uuids["client_2"]

        # Client 1 creates prompts for different use cases
        client1_prompts = [
            "You are a customer service assistant.",
            "You are a technical support specialist.",
            "You are a sales representative."
        ]

        # Client 2 creates prompts for different domains
        client2_prompts = [
            "You are a medical information assistant.",
            "You are a legal research assistant."
        ]

        # Create prompts for both clients
        client1_ids = []
        for prompt in client1_prompts:
            prompt_id = prompt_store.create_prompt(client1, prompt)
            client1_ids.append(prompt_id)

        client2_ids = []
        for prompt in client2_prompts:
            prompt_id = prompt_store.create_prompt(client2, prompt)
            client2_ids.append(prompt_id)

        # Verify each client sees only their prompts
        c1_list = prompt_store.list_prompts(client1)
        c2_list = prompt_store.list_prompts(client2)

        assert len(c1_list) == 3
        assert len(c2_list) == 2

        # Update one prompt for each client
        prompt_store.update_prompt(client1_ids[0], client1, "Updated: " + client1_prompts[0])
        prompt_store.update_prompt(client2_ids[0], client2, "Updated: " + client2_prompts[0])

        # Verify updates don't affect other client
        c1_updated = prompt_store.get_prompt(client1_ids[0], client1)
        c2_updated = prompt_store.get_prompt(client2_ids[0], client2)

        assert c1_updated.prompt.startswith("Updated: You are a customer")
        assert c2_updated.prompt.startswith("Updated: You are a medical")

        # Delete one prompt from client1, verify client2 unaffected
        prompt_store.delete_prompt(client1_ids[1], client1)

        c1_final = prompt_store.list_prompts(client1)
        c2_final = prompt_store.list_prompts(client2)

        assert len(c1_final) == 2  # One deleted
        assert len(c2_final) == 2  # Unchanged
