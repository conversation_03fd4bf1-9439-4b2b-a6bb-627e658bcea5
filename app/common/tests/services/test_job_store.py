"""Tests for JobStore service."""

import uuid
from typing import Any
from unittest.mock import patch

import pytest

from common.services import JobStore
from common.services.job_store import BatchJobStatusEnum, Job


class TestJobStoreCreation:
    """Test JobStore creation and initialization."""

    def test_job_store_initialization(self, job_store: JobStore):
        """Test JobStore initializes correctly."""
        assert job_store.table_name == "test_batch_jobs"
        assert job_store.schema_name == "public"
        # Test public interface rather than private attributes
        assert hasattr(job_store, '_table_class')
        assert hasattr(job_store, '_session')
        assert hasattr(job_store, '_async_session')

    def test_from_params_creation(self):
        """Test JobStore creation from parameters."""
        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_jobs",
                schema_name="custom_schema",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "custom_jobs"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test JobStore creation from URI."""
        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_jobs",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "uri_jobs"
            assert store.schema_name == "public"

    @patch('common.services.job_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.job_store.create_engine'), \
                patch('common.services.job_store.create_async_engine'), \
                patch('common.services.job_store.sessionmaker'):

            store = JobStore.get_instance()
            assert store.table_name == "batch_jobs"


class TestJobStoreBasicOperations:
    """Test basic job operations."""

    def test_create_job_sync(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test synchronous job creation."""
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"],
            status=sample_job_data["status"]
        )

        assert isinstance(job_id, uuid.UUID)

        # Verify job was created
        job = job_store.get_job(job_id)
        assert job.client_guid == sample_job_data["client_guid"]
        assert job.total_count == sample_job_data["total_count"]
        assert job.job_params == sample_job_data["job_params"]
        assert job.status == sample_job_data["status"]
        assert job.processed_count == 0
        assert job.created_at is not None
        assert job.updated_at is not None

    @pytest.mark.asyncio
    async def test_create_job_async(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test asynchronous job creation."""
        job_id = await job_store.async_create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"],
            status=sample_job_data["status"]
        )

        assert isinstance(job_id, uuid.UUID)

        # Verify job was created
        job = await job_store.async_get_job(job_id)
        assert job.client_guid == sample_job_data["client_guid"]
        assert job.total_count == sample_job_data["total_count"]
        assert job.job_params == sample_job_data["job_params"]
        assert job.status == sample_job_data["status"]

    def test_get_job_sync(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test synchronous job retrieval."""
        # Create job first
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        # Retrieve job
        job = job_store.get_job(job_id)

        assert isinstance(job, Job)
        assert job.id == job_id
        assert job.client_guid == sample_job_data["client_guid"]
        assert job.total_count == sample_job_data["total_count"]
        assert job.job_params == sample_job_data["job_params"]

    @pytest.mark.asyncio
    async def test_get_job_async(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test asynchronous job retrieval."""
        # Create job first
        job_id = await job_store.async_create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        # Retrieve job
        job = await job_store.async_get_job(job_id)

        assert isinstance(job, Job)
        assert job.id == job_id
        assert job.client_guid == sample_job_data["client_guid"]
        assert job.total_count == sample_job_data["total_count"]

    def test_get_nonexistent_job_sync(self, job_store: JobStore):
        """Test retrieving non-existent job synchronously."""
        fake_id = uuid.uuid4()

        with pytest.raises(LookupError, match="Job not found"):
            job_store.get_job(fake_id)

    @pytest.mark.asyncio
    async def test_get_nonexistent_job_async(self, job_store: JobStore):
        """Test retrieving non-existent job asynchronously."""
        fake_id = uuid.uuid4()

        with pytest.raises(LookupError, match="Job not found"):
            await job_store.async_get_job(fake_id)


class TestJobStoreStatusManagement:
    """Test job status transitions and management."""

    def test_job_status_enum_values(self):
        """Test that all expected status values are available."""
        assert BatchJobStatusEnum.PENDING == 0
        assert BatchJobStatusEnum.PROCESSING == 1
        assert BatchJobStatusEnum.COMPLETED == 2
        assert BatchJobStatusEnum.FAILED == 3
        assert BatchJobStatusEnum.PARTIALLY_COMPLETED == 4

    def test_update_job_status_sync(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test updating job status synchronously."""
        # Create job
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        # Get initial job
        job = job_store.get_job(job_id)
        assert job.status == BatchJobStatusEnum.PENDING

        # Update to processing
        job.status = BatchJobStatusEnum.PROCESSING
        job.processed_count = 3
        job.status_description = "Processing batch items"

        success = job_store.update_job(job)
        assert success is True

        # Verify update
        updated_job = job_store.get_job(job_id)
        assert updated_job.status == BatchJobStatusEnum.PROCESSING
        assert updated_job.processed_count == 3
        assert updated_job.status_description == "Processing batch items"

    @pytest.mark.asyncio
    async def test_update_job_status_async(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test updating job status asynchronously."""
        # Create job
        job_id = await job_store.async_create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        # Get initial job
        job = await job_store.async_get_job(job_id)
        assert job.status == BatchJobStatusEnum.PENDING

        # Update to completed
        job.status = BatchJobStatusEnum.COMPLETED
        job.processed_count = job.total_count
        job.status_description = None  # Completed jobs don't need description

        success = await job_store.async_update_job(job)
        assert success is True

        # Verify update
        updated_job = await job_store.async_get_job(job_id)
        assert updated_job.status == BatchJobStatusEnum.COMPLETED
        assert updated_job.processed_count == updated_job.total_count

    def test_job_progress_tracking(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test job progress tracking through status updates."""
        # Create job with 10 total items
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=10,
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)

        # Simulate processing progress
        progress_updates = [
            (BatchJobStatusEnum.PROCESSING, 2, "Started processing"),
            (BatchJobStatusEnum.PROCESSING, 5, "Half way through"),
            (BatchJobStatusEnum.PROCESSING, 8, "Almost done"),
            (BatchJobStatusEnum.COMPLETED, 10, None),
        ]

        for status, processed, description in progress_updates:
            job.status = status
            job.processed_count = processed
            job.status_description = description

            job_store.update_job(job)

            # Verify each update
            updated_job = job_store.get_job(job_id)
            assert updated_job.status == status
            assert updated_job.processed_count == processed
            assert updated_job.status_description == description

    def test_failed_job_handling(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test handling of failed jobs."""
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)

        # Simulate failure
        job.status = BatchJobStatusEnum.FAILED
        job.processed_count = 3  # Only processed 3 out of total
        job.status_description = "Failed due to API rate limit exceeded"

        job_store.update_job(job)

        # Verify failure state
        failed_job = job_store.get_job(job_id)
        assert failed_job.status == BatchJobStatusEnum.FAILED
        assert failed_job.processed_count == 3
        assert failed_job.processed_count < failed_job.total_count
        assert "rate limit" in failed_job.status_description

    def test_partially_completed_job(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test handling of partially completed jobs."""
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=10,
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)

        # Simulate partial completion
        job.status = BatchJobStatusEnum.PARTIALLY_COMPLETED
        job.processed_count = 7  # 7 out of 10 completed
        job.status_description = "3 items failed validation"

        job_store.update_job(job)

        # Verify partial completion state
        partial_job = job_store.get_job(job_id)
        assert partial_job.status == BatchJobStatusEnum.PARTIALLY_COMPLETED
        assert partial_job.processed_count == 7
        assert partial_job.processed_count < partial_job.total_count
        assert partial_job.processed_count > 0


class TestJobStoreDataIntegrity:
    """Test data integrity and complex job parameters."""

    def test_complex_job_parameters(self, job_store: JobStore, test_uuids: dict[str, uuid.UUID]):
        """Test storing complex job parameters."""
        complex_params = {
            "model_config": {
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000,
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1
            },
            "batch_config": {
                "batch_size": 50,
                "max_retries": 3,
                "retry_delay": 5.0,
                "timeout": 30.0
            },
            "output_config": {
                "format": "json",
                "schema": {
                    "type": "object",
                    "properties": {
                        "response": {"type": "string"},
                        "confidence": {"type": "number"}
                    }
                }
            },
            "metadata": {
                "created_by": "test_user",
                "project": "test_project",
                "tags": ["production", "batch_processing"],
                "priority": "high"
            }
        }

        job_id = job_store.create_job(
            client_guid=test_uuids["client_1"],
            total_count=100,
            job_params=complex_params
        )

        # Retrieve and verify complex parameters
        job = job_store.get_job(job_id)
        assert job.job_params == complex_params
        assert job.job_params["model_config"]["model"] == "gpt-4"
        assert job.job_params["batch_config"]["batch_size"] == 50
        assert job.job_params["metadata"]["tags"] == ["production", "batch_processing"]

    def test_job_results_storage(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test storing and retrieving job results."""
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=3,
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)

        # Simulate processing with results
        job.results = [
            {
                "item_id": "item_1",
                "status": "success",
                "output": "Generated response 1",
                "tokens_used": 150,
                "processing_time": 1.23
            },
            {
                "item_id": "item_2",
                "status": "success",
                "output": "Generated response 2",
                "tokens_used": 200,
                "processing_time": 1.45
            },
            {
                "item_id": "item_3",
                "status": "failed",
                "error": "Rate limit exceeded",
                "retry_count": 3
            }
        ]
        job.processed_count = 3
        job.status = BatchJobStatusEnum.PARTIALLY_COMPLETED

        job_store.update_job(job)

        # Verify results storage
        updated_job = job_store.get_job(job_id)
        assert len(updated_job.results) == 3
        assert updated_job.results[0]["status"] == "success"
        assert updated_job.results[2]["status"] == "failed"
        assert updated_job.results[1]["tokens_used"] == 200

    def test_timestamp_handling(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test proper timestamp handling throughout job lifecycle."""
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)

        # Initial state - only created_at and updated_at should be set
        assert job.created_at is not None
        assert job.updated_at is not None
        assert job.started_at is None
        assert job.completed_at is None
        assert job.created_at == job.updated_at  # Should be same for new job

        # Start processing
        from common.utils import get_current_time
        start_time = get_current_time()
        job.status = BatchJobStatusEnum.PROCESSING
        job.started_at = start_time

        job_store.update_job(job)

        # Verify started_at is set and updated_at changed
        processing_job = job_store.get_job(job_id)
        assert processing_job.started_at is not None
        assert processing_job.completed_at is None
        assert processing_job.updated_at > processing_job.created_at

        # Complete job
        completion_time = get_current_time()
        processing_job.status = BatchJobStatusEnum.COMPLETED
        processing_job.completed_at = completion_time
        processing_job.processed_count = processing_job.total_count

        job_store.update_job(processing_job)

        # Verify completion timestamp
        completed_job = job_store.get_job(job_id)
        assert completed_job.completed_at is not None
        assert completed_job.completed_at >= completed_job.started_at

    def test_client_isolation(self, job_store: JobStore, test_uuids: dict[str, uuid.UUID]):
        """Test that jobs are properly isolated by client."""
        client1_jobs = []
        client2_jobs = []

        # Create jobs for different clients
        for i in range(3):
            job_id1 = job_store.create_job(
                client_guid=test_uuids["client_1"],
                total_count=10,
                job_params={"client": "client_1", "job_number": i}
            )
            client1_jobs.append(job_id1)

            job_id2 = job_store.create_job(
                client_guid=test_uuids["client_2"],
                total_count=20,
                job_params={"client": "client_2", "job_number": i}
            )
            client2_jobs.append(job_id2)

        # Verify each client's jobs
        for i, job_id in enumerate(client1_jobs):
            job = job_store.get_job(job_id)
            assert job.client_guid == test_uuids["client_1"]
            assert job.total_count == 10
            assert job.job_params["client"] == "client_1"
            assert job.job_params["job_number"] == i

        for i, job_id in enumerate(client2_jobs):
            job = job_store.get_job(job_id)
            assert job.client_guid == test_uuids["client_2"]
            assert job.total_count == 20
            assert job.job_params["client"] == "client_2"
            assert job.job_params["job_number"] == i


class TestJobStoreIntegration:
    """Integration tests for JobStore."""

    @pytest.mark.asyncio
    async def test_sync_async_consistency(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test that sync and async operations are consistent."""
        # Create job synchronously
        sync_job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params={"method": "sync", **sample_job_data["job_params"]}
        )

        # Create job asynchronously
        async_job_id = await job_store.async_create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=sample_job_data["total_count"],
            job_params={"method": "async", **sample_job_data["job_params"]}
        )

        # Retrieve both jobs using both methods
        sync_job_sync_get = job_store.get_job(sync_job_id)
        sync_job_async_get = await job_store.async_get_job(sync_job_id)
        async_job_sync_get = job_store.get_job(async_job_id)
        async_job_async_get = await job_store.async_get_job(async_job_id)

        # Verify consistency
        assert sync_job_sync_get.job_params["method"] == sync_job_async_get.job_params["method"] == "sync"
        assert async_job_sync_get.job_params["method"] == async_job_async_get.job_params["method"] == "async"
        assert sync_job_sync_get.total_count == sync_job_async_get.total_count
        assert async_job_sync_get.total_count == async_job_async_get.total_count

    def test_complete_job_lifecycle(self, job_store: JobStore, sample_job_data: dict[str, Any]):
        """Test a complete job lifecycle from creation to completion."""
        from common.utils import get_current_time

        # 1. Create job
        job_id = job_store.create_job(
            client_guid=sample_job_data["client_guid"],
            total_count=5,
            job_params=sample_job_data["job_params"]
        )

        job = job_store.get_job(job_id)
        assert job.status == BatchJobStatusEnum.PENDING
        assert job.processed_count == 0

        # 2. Start processing
        job.status = BatchJobStatusEnum.PROCESSING
        job.started_at = get_current_time()
        job_store.update_job(job)

        # 3. Process items incrementally
        for i in range(1, 6):
            job = job_store.get_job(job_id)
            job.processed_count = i
            job.results.append({
                "item": i,
                "status": "completed",
                "timestamp": get_current_time().isoformat()
            })

            if i == 5:  # Last item
                job.status = BatchJobStatusEnum.COMPLETED
                job.completed_at = get_current_time()

            job_store.update_job(job)

        # 4. Verify final state
        final_job = job_store.get_job(job_id)
        assert final_job.status == BatchJobStatusEnum.COMPLETED
        assert final_job.processed_count == 5
        assert final_job.processed_count == final_job.total_count
        assert len(final_job.results) == 5
        assert final_job.started_at is not None
        assert final_job.completed_at is not None
        assert final_job.completed_at >= final_job.started_at
