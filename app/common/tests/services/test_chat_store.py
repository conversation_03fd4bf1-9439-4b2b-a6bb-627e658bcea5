"""Tests for PostgresChatStore service."""

import uuid
from typing import Any
from unittest.mock import patch

import pytest
from llama_index.core.llms import ChatMessage

from common.services import PostgresChatStore


class TestChatStoreCreation:
    """Test PostgresChatStore creation and initialization."""

    def test_chat_store_initialization(self, chat_store: PostgresChatStore):
        """Test ChatStore initializes correctly."""
        assert chat_store.table_name == "test_chatstore"
        assert chat_store.schema_name == "public"
        # Test public interface rather than private attributes
        assert hasattr(chat_store, '_table_class')
        assert hasattr(chat_store, '_session')
        assert hasattr(chat_store, '_async_session')

    def test_from_params_creation(self):
        """Test ChatStore creation from parameters."""
        with patch('common.services.chat_store.create_engine'), \
                patch('common.services.chat_store.create_async_engine'), \
                patch('common.services.chat_store.sessionmaker'):

            store = PostgresChatStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_chatstore",
                schema_name="custom_schema",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "custom_chatstore"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test ChatStore creation from URI."""
        with patch('common.services.chat_store.create_engine'), \
                patch('common.services.chat_store.create_async_engine'), \
                patch('common.services.chat_store.sessionmaker'):

            store = PostgresChatStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_chatstore",
                debug=True,
                use_jsonb=True
            )

            assert store.table_name == "uri_chatstore"
            assert store.schema_name == "public"


class TestChatStoreBasicOperations:
    """Test basic chat message operations."""

    def test_add_message_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test adding a single message synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = ChatMessage(role="user", content="Hello, world!")

        chat_store.add_message(client_guid, user_guid, message)

        # Retrieve and verify
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == 1
        assert messages[0].role == "user"
        assert messages[0].content == "Hello, world!"

    @pytest.mark.asyncio
    async def test_add_message_async(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test adding a single message asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = ChatMessage(role="assistant", content="Hello back!")

        await chat_store.aadd_message(client_guid, user_guid, message)

        # Retrieve and verify
        messages = await chat_store.aget_messages(client_guid, user_guid)
        assert len(messages) == 1
        assert messages[0].role == "assistant"
        assert messages[0].content == "Hello back!"

    def test_get_messages_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test retrieving messages synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add multiple messages
        for message in sample_chat_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Retrieve all messages
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == len(sample_chat_messages)

        # Verify order (should be chronological)
        for i, message in enumerate(messages):
            assert message.role == sample_chat_messages[i].role
            assert message.content == sample_chat_messages[i].content

    @pytest.mark.asyncio
    async def test_get_messages_async(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test retrieving messages asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add multiple messages
        for message in sample_chat_messages:
            await chat_store.aadd_message(client_guid, user_guid, message)

        # Retrieve all messages
        messages = await chat_store.aget_messages(client_guid, user_guid)
        assert len(messages) == len(sample_chat_messages)

        # Verify content
        for i, message in enumerate(messages):
            assert message.role == sample_chat_messages[i].role
            assert message.content == sample_chat_messages[i].content

    def test_get_messages_with_limit(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test retrieving messages with limit."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add multiple messages
        for message in sample_chat_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Retrieve with limit
        limited_messages = chat_store.get_messages(client_guid, user_guid, top=2)
        assert len(limited_messages) == 2

        # Should get the first 2 messages (chronological order)
        assert limited_messages[0].content == sample_chat_messages[0].content
        assert limited_messages[1].content == sample_chat_messages[1].content


class TestChatStoreAdvancedOperations:
    """Test advanced chat store operations."""

    def test_set_messages_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test setting messages (replace all) synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # First add some messages
        initial_message = ChatMessage(role="user", content="Initial message")
        chat_store.add_message(client_guid, user_guid, initial_message)

        # Verify initial state
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == 1

        # Set new messages (should replace all)
        chat_store.set_messages(client_guid, user_guid, sample_chat_messages)

        # Verify replacement
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == len(sample_chat_messages)
        for i, message in enumerate(messages):
            assert message.content == sample_chat_messages[i].content

    @pytest.mark.asyncio
    async def test_set_messages_async(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test setting messages (replace all) asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # First add some messages
        initial_message = ChatMessage(role="user", content="Initial async message")
        await chat_store.aadd_message(client_guid, user_guid, initial_message)

        # Set new messages (should replace all)
        await chat_store.aset_messages(client_guid, user_guid, sample_chat_messages)

        # Verify replacement
        messages = await chat_store.aget_messages(client_guid, user_guid)
        assert len(messages) == len(sample_chat_messages)
        for i, message in enumerate(messages):
            assert message.content == sample_chat_messages[i].content

    def test_delete_messages_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test deleting all messages synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages
        for message in sample_chat_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Verify messages exist
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == len(sample_chat_messages)

        # Delete all messages
        deleted_messages = chat_store.delete_messages(client_guid, user_guid)

        # Verify deletion
        assert len(deleted_messages) == len(sample_chat_messages)
        remaining_messages = chat_store.get_messages(client_guid, user_guid)
        assert len(remaining_messages) == 0

    @pytest.mark.asyncio
    async def test_delete_messages_async(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test deleting all messages asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages
        for message in sample_chat_messages:
            await chat_store.aadd_message(client_guid, user_guid, message)

        # Delete all messages
        deleted_messages = await chat_store.adelete_messages(client_guid, user_guid)

        # Verify deletion
        assert len(deleted_messages) == len(sample_chat_messages)
        remaining_messages = await chat_store.aget_messages(client_guid, user_guid)
        assert len(remaining_messages) == 0

    def test_delete_last_message_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test deleting the last message synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages
        for message in sample_chat_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Delete last message
        deleted_message = chat_store.delete_last_message(client_guid, user_guid)

        # Verify deletion
        assert deleted_message is not None
        assert deleted_message.content == sample_chat_messages[-1].content

        remaining_messages = chat_store.get_messages(client_guid, user_guid)
        assert len(remaining_messages) == len(sample_chat_messages) - 1

    @pytest.mark.asyncio
    async def test_delete_last_message_async(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test deleting the last message asynchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages
        for message in sample_chat_messages:
            await chat_store.aadd_message(client_guid, user_guid, message)

        # Delete last message
        deleted_message = await chat_store.adelete_last_message(client_guid, user_guid)

        # Verify deletion
        assert deleted_message is not None
        assert deleted_message.content == sample_chat_messages[-1].content

        remaining_messages = await chat_store.aget_messages(client_guid, user_guid)
        assert len(remaining_messages) == len(sample_chat_messages) - 1

    def test_delete_last_message_empty_conversation(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test deleting last message when no messages exist."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Try to delete from empty conversation
        deleted_message = chat_store.delete_last_message(client_guid, user_guid)
        assert deleted_message is None

    def test_update_last_message_sync(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test updating the last message synchronously."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages
        for message in sample_chat_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Update last message
        update_data = {"status": "edited", "edit_timestamp": "2023-01-01T00:00:00Z"}
        old_message = chat_store.update_last_message(client_guid, user_guid, **update_data)

        # Verify update
        assert old_message is not None
        assert old_message.content == sample_chat_messages[-1].content

        # Get updated message
        messages = chat_store.get_messages(client_guid, user_guid)
        last_message = messages[-1]
        assert last_message.additional_kwargs["status"] == "edited"
        assert last_message.additional_kwargs["edit_timestamp"] == "2023-01-01T00:00:00Z"


class TestChatStoreClientUserScoping:
    """Test client-user isolation and scoping."""

    def test_client_user_isolation(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test that messages are properly isolated between client-user pairs."""
        client1 = test_uuids["client_1"]
        client2 = test_uuids["client_2"]
        user1 = test_uuids["user_1"]
        user2 = test_uuids["user_2"]

        # Add messages for different client-user combinations
        msg1 = ChatMessage(role="user", content="Client1-User1 message")
        msg2 = ChatMessage(role="user", content="Client1-User2 message")
        msg3 = ChatMessage(role="user", content="Client2-User1 message")
        msg4 = ChatMessage(role="user", content="Client2-User2 message")

        chat_store.add_message(client1, user1, msg1)
        chat_store.add_message(client1, user2, msg2)
        chat_store.add_message(client2, user1, msg3)
        chat_store.add_message(client2, user2, msg4)

        # Verify isolation
        c1u1_messages = chat_store.get_messages(client1, user1)
        c1u2_messages = chat_store.get_messages(client1, user2)
        c2u1_messages = chat_store.get_messages(client2, user1)
        c2u2_messages = chat_store.get_messages(client2, user2)

        assert len(c1u1_messages) == 1
        assert len(c1u2_messages) == 1
        assert len(c2u1_messages) == 1
        assert len(c2u2_messages) == 1

        assert c1u1_messages[0].content == "Client1-User1 message"
        assert c1u2_messages[0].content == "Client1-User2 message"
        assert c2u1_messages[0].content == "Client2-User1 message"
        assert c2u2_messages[0].content == "Client2-User2 message"

    def test_cross_client_operations_dont_interfere(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test that operations on one client don't affect another."""
        client1 = test_uuids["client_1"]
        client2 = test_uuids["client_2"]
        user1 = test_uuids["user_1"]

        # Add messages for both clients
        for message in sample_chat_messages:
            chat_store.add_message(client1, user1, message)
            chat_store.add_message(client2, user1, message)

        # Delete messages for client1 only
        chat_store.delete_messages(client1, user1)

        # Verify client1 messages are deleted but client2 messages remain
        c1_messages = chat_store.get_messages(client1, user1)
        c2_messages = chat_store.get_messages(client2, user1)

        assert len(c1_messages) == 0
        assert len(c2_messages) == len(sample_chat_messages)

    def test_empty_conversation_handling(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of empty conversations."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Get messages from empty conversation
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == 0

        # Try operations on empty conversation
        deleted_messages = chat_store.delete_messages(client_guid, user_guid)
        assert deleted_messages == []

        deleted_last = chat_store.delete_last_message(client_guid, user_guid)
        assert deleted_last is None

        updated_last = chat_store.update_last_message(client_guid, user_guid, test="value")
        assert updated_last is None


class TestChatStoreDataIntegrity:
    """Test data integrity and message handling."""

    def test_message_with_additional_kwargs(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test storing messages with additional metadata."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        message = ChatMessage(
            role="assistant",
            content="Response with metadata",
            additional_kwargs={
                "model": "gpt-4",
                "temperature": 0.7,
                "tokens_used": 150,
                "response_time": 1.23,
                "metadata": {"source": "api", "version": "1.0"}
            }
        )

        chat_store.add_message(client_guid, user_guid, message)

        # Retrieve and verify metadata preservation
        messages = chat_store.get_messages(client_guid, user_guid)
        retrieved_message = messages[0]

        assert retrieved_message.additional_kwargs["model"] == "gpt-4"
        assert retrieved_message.additional_kwargs["temperature"] == 0.7
        assert retrieved_message.additional_kwargs["tokens_used"] == 150
        assert retrieved_message.additional_kwargs["response_time"] == 1.23
        assert retrieved_message.additional_kwargs["metadata"]["source"] == "api"

    def test_unicode_content_handling(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of unicode content in messages."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        unicode_messages = [
            ChatMessage(role="user", content="Hello in Chinese: 你好世界"),
            ChatMessage(role="assistant", content="Emoji response: 🤖 I understand! 🌟"),
            ChatMessage(role="user", content="Math symbols: ∑∞∫∂∇"),
            ChatMessage(role="assistant", content="Special chars: àáâãäåæçèéêë"),
        ]

        # Add unicode messages
        for message in unicode_messages:
            chat_store.add_message(client_guid, user_guid, message)

        # Retrieve and verify
        retrieved_messages = chat_store.get_messages(client_guid, user_guid)
        assert len(retrieved_messages) == len(unicode_messages)

        for i, message in enumerate(retrieved_messages):
            assert message.content == unicode_messages[i].content
            assert message.role == unicode_messages[i].role

    def test_large_message_content(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test handling of large message content."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Create a large message (simulating a long conversation or code)
        large_content = "This is a very long message. " * 1000  # ~30KB
        large_message = ChatMessage(role="assistant", content=large_content)

        chat_store.add_message(client_guid, user_guid, large_message)

        # Retrieve and verify
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == 1
        assert messages[0].content == large_content
        assert len(messages[0].content) > 25000  # Verify it's actually large


class TestChatStoreIntegration:
    """Integration tests for ChatStore."""

    @pytest.mark.asyncio
    async def test_sync_async_consistency(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID], sample_chat_messages: list[ChatMessage]):
        """Test that sync and async operations are consistent."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Add messages using sync method
        for i, message in enumerate(sample_chat_messages[:2]):
            if i % 2 == 0:
                chat_store.add_message(client_guid, user_guid, message)
            else:
                await chat_store.aadd_message(client_guid, user_guid, message)

        # Retrieve using both methods
        sync_messages = chat_store.get_messages(client_guid, user_guid)
        async_messages = await chat_store.aget_messages(client_guid, user_guid)

        # Verify consistency
        assert len(sync_messages) == len(async_messages) == 2
        for i in range(len(sync_messages)):
            assert sync_messages[i].content == async_messages[i].content
            assert sync_messages[i].role == async_messages[i].role

    def test_conversation_workflow(self, chat_store: PostgresChatStore, test_uuids: dict[str, uuid.UUID]):
        """Test a realistic conversation workflow."""
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Simulate a conversation workflow
        # 1. User starts conversation
        user_msg1 = ChatMessage(role="user", content="Hello, I need help with Python")
        chat_store.add_message(client_guid, user_guid, user_msg1)

        # 2. Assistant responds
        assistant_msg1 = ChatMessage(role="assistant", content="I'd be happy to help with Python! What specific topic?")
        chat_store.add_message(client_guid, user_guid, assistant_msg1)

        # 3. User asks specific question
        user_msg2 = ChatMessage(role="user", content="How do I handle exceptions?")
        chat_store.add_message(client_guid, user_guid, user_msg2)

        # 4. Assistant provides detailed response
        assistant_msg2 = ChatMessage(
            role="assistant",
            content="Here's how to handle exceptions in Python...",
            additional_kwargs={"code_example": True, "language": "python"}
        )
        chat_store.add_message(client_guid, user_guid, assistant_msg2)

        # 5. User wants to edit their last message
        chat_store.update_last_message(client_guid, user_guid, edited=True, edit_reason="typo_fix")

        # Verify final conversation state
        messages = chat_store.get_messages(client_guid, user_guid)
        assert len(messages) == 4

        # Check conversation flow
        assert messages[0].role == "user"
        assert messages[1].role == "assistant"
        assert messages[2].role == "user"
        assert messages[3].role == "assistant"

        # Check that last message was updated
        assert messages[3].additional_kwargs["edited"] is True
        assert messages[3].additional_kwargs["code_example"] is True
