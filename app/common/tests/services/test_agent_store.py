"""Tests for AgentStore service."""

import uuid
from typing import Any
from unittest.mock import patch

import pytest
from sqlalchemy.exc import OperationalError

from common.services import AgentStore
from common.services.agent_store import AgentEntry


class TestAgentStoreCreation:
    """Test AgentStore creation and initialization."""

    def test_agent_store_initialization(self, agent_store: AgentStore):
        """Test AgentStore initializes correctly."""
        assert agent_store.table_name == "test_agents"
        assert agent_store.schema_name == "public"
        # Test public interface rather than private attributes
        assert hasattr(agent_store, '_table_class')
        assert hasattr(agent_store, '_session')
        assert hasattr(agent_store, '_async_session')

    def test_from_params_creation(self):
        """Test AgentStore creation from parameters."""
        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.from_params(
                host="localhost",
                port="5432",
                database="test_db",
                user="test_user",
                password="test_pass",
                table_name="custom_agents",
                schema_name="custom_schema",
                debug=True
            )

            assert store.table_name == "custom_agents"
            assert store.schema_name == "custom_schema"

    def test_from_uri_creation(self):
        """Test AgentStore creation from URI."""
        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.from_uri(
                uri="postgresql://user:pass@localhost:5432/",
                db_name="test_db",
                table_name="uri_agents",
                debug=True
            )

            assert store.table_name == "uri_agents"
            assert store.schema_name == "public"

    @patch('common.services.agent_store.settings')
    def test_get_instance(self, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = "postgresql://test:test@localhost:5432/"
        mock_settings.database.db_name = "test_db"

        with patch('common.services.agent_store.create_engine'), \
                patch('common.services.agent_store.create_async_engine'), \
                patch('common.services.agent_store.sessionmaker'):

            store = AgentStore.get_instance()
            assert store.table_name == "agents"


class TestAgentStoreOperations:
    """Test AgentStore CRUD operations."""

    def test_create_agent_sync(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test synchronous agent creation."""
        agent_id = agent_store.create_agent(
            name=sample_agent_data["name"],
            definition=sample_agent_data["definition"]
        )

        assert isinstance(agent_id, uuid.UUID)

        # Verify agent was created
        agent = agent_store.get_agent(agent_id)
        assert agent.name == sample_agent_data["name"]
        assert agent.definition == sample_agent_data["definition"]
        assert agent.created_at is not None
        assert agent.updated_at is not None

    @pytest.mark.asyncio
    async def test_create_agent_async(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test asynchronous agent creation."""
        agent_id = await agent_store.async_create_agent(
            name=sample_agent_data["name"],
            definition=sample_agent_data["definition"]
        )

        assert isinstance(agent_id, uuid.UUID)

        # Verify agent was created
        agent = await agent_store.async_get_agent(agent_id)
        assert agent.name == sample_agent_data["name"]
        assert agent.definition == sample_agent_data["definition"]

    def test_get_agent_sync(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test synchronous agent retrieval."""
        # Create agent first
        agent_id = agent_store.create_agent(
            name=sample_agent_data["name"],
            definition=sample_agent_data["definition"]
        )

        # Retrieve agent
        agent = agent_store.get_agent(agent_id)

        assert isinstance(agent, AgentEntry)
        assert agent.id == agent_id
        assert agent.name == sample_agent_data["name"]
        assert agent.definition == sample_agent_data["definition"]

    @pytest.mark.asyncio
    async def test_get_agent_async(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test asynchronous agent retrieval."""
        # Create agent first
        agent_id = await agent_store.async_create_agent(
            name=sample_agent_data["name"],
            definition=sample_agent_data["definition"]
        )

        # Retrieve agent
        agent = await agent_store.async_get_agent(agent_id)

        assert isinstance(agent, AgentEntry)
        assert agent.id == agent_id
        assert agent.name == sample_agent_data["name"]
        assert agent.definition == sample_agent_data["definition"]

    def test_get_nonexistent_agent_sync(self, agent_store: AgentStore):
        """Test retrieving non-existent agent synchronously."""
        fake_id = uuid.uuid4()

        with pytest.raises(LookupError, match=r"Agent not found"):
            agent_store.get_agent(fake_id)

    @pytest.mark.asyncio
    async def test_get_nonexistent_agent_async(self, agent_store: AgentStore):
        """Test retrieving non-existent agent asynchronously."""
        fake_id = uuid.uuid4()

        with pytest.raises(LookupError, match=r"Agent not found"):
            await agent_store.async_get_agent(fake_id)


class TestAgentStoreDataIntegrity:
    """Test data integrity and validation."""

    def test_agent_timestamps(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test that timestamps are properly set and maintained."""
        agent_id = agent_store.create_agent(
            name=sample_agent_data["name"],
            definition=sample_agent_data["definition"]
        )

        agent = agent_store.get_agent(agent_id)

        # Timestamps should be set
        assert agent.created_at is not None
        assert agent.updated_at is not None
        # For new records, created_at and updated_at should be the same
        assert agent.created_at == agent.updated_at

    def test_complex_definition_storage(self, agent_store: AgentStore):
        """Test storing complex JSON definitions."""
        complex_definition: dict[str, Any] = {
            "workflow": {
                "steps": [
                    {
                        "id": "step1",
                        "type": "llm_call",
                        "config": {
                            "model": "gpt-4",
                            "temperature": 0.7,
                            "max_tokens": 1000,
                            "system_prompt": "You are a helpful assistant"
                        },
                        "inputs": ["user_query"],
                        "outputs": ["response"]
                    },
                    {
                        "id": "step2",
                        "type": "validation",
                        "config": {
                            "rules": ["length > 10", "contains_answer"],
                            "fallback": "default_response"
                        }
                    }
                ],
                "connections": [
                    {"from": "step1", "to": "step2", "condition": "success"}
                ]
            },
            "metadata": {
                "version": "2.1.0",
                "author": "test_user",
                "tags": ["production", "validated"],
                "created": "2023-01-01T00:00:00Z"
            }
        }

        agent_id = agent_store.create_agent(
            name="complex_agent",
            definition=complex_definition
        )

        retrieved_agent = agent_store.get_agent(agent_id)
        assert retrieved_agent.definition == complex_definition

    def test_unicode_and_special_characters(self, agent_store: AgentStore):
        """Test handling of unicode and special characters."""
        special_definition: dict[str, Any] = {
            "name": "测试代理",  # Chinese characters
            "description": "Agent with émojis 🤖 and spëcial chars",
            "config": {
                "prompt": "Respond in français: Bonjour! 你好! 🌟",
                "symbols": "!@#$%^&*()_+-=[]{}|;':\",./<>?"
            }
        }

        agent_id = agent_store.create_agent(
            name="unicode_test_agent",
            definition=special_definition
        )

        retrieved_agent = agent_store.get_agent(agent_id)
        assert retrieved_agent.definition == special_definition


class TestAgentStoreErrorHandling:
    """Test error handling and edge cases."""

    def test_connection_error_handling(self):
        """Test handling of database connection errors."""
        with patch('common.services.agent_store.create_engine') as mock_engine:
            mock_engine.side_effect = OperationalError("Connection failed", {}, None)

            with pytest.raises(OperationalError):
                AgentStore.from_params(
                    host="invalid_host",
                    port="5432",
                    database="test_db",
                    user="test_user",
                    password="test_pass"
                )

    def test_invalid_uuid_handling(self, agent_store: AgentStore):
        """Test handling of invalid UUID formats."""
        # This test verifies that the UUID type checking works correctly
        # The UUID should be properly validated by the UUID type
        fake_id = uuid.uuid4()

        with pytest.raises(LookupError):
            agent_store.get_agent(fake_id)

    def test_empty_definition_handling(self, agent_store: AgentStore):
        """Test handling of empty or minimal definitions."""
        # Test with empty dict
        agent_id = agent_store.create_agent(
            name="empty_definition_agent",
            definition={}
        )

        agent = agent_store.get_agent(agent_id)
        assert agent.definition == {}

        # Test with minimal definition
        minimal_definition = {"type": "simple"}
        agent_id2 = agent_store.create_agent(
            name="minimal_agent",
            definition=minimal_definition
        )

        agent2 = agent_store.get_agent(agent_id2)
        assert agent2.definition == minimal_definition


class TestAgentStoreIntegration:
    """Integration tests for AgentStore."""

    def test_multiple_agents_isolation(self, agent_store: AgentStore):
        """Test that multiple agents are properly isolated."""
        # Create multiple agents
        agents_data: list[dict[str, Any]] = [
            {"name": "agent_1", "definition": {"type": "type1", "config": {"param": "value1"}}},
            {"name": "agent_2", "definition": {"type": "type2", "config": {"param": "value2"}}},
            {"name": "agent_3", "definition": {"type": "type3", "config": {"param": "value3"}}},
        ]

        agent_ids = []
        for agent_data in agents_data:
            agent_id = agent_store.create_agent(
                name=agent_data["name"],
                definition=agent_data["definition"]
            )
            agent_ids.append(agent_id)

        # Verify each agent is correctly stored and isolated
        for i, agent_id in enumerate(agent_ids):
            agent = agent_store.get_agent(agent_id)
            assert agent.name == agents_data[i]["name"]
            assert agent.definition == agents_data[i]["definition"]
            assert agent.id == agent_id

    @pytest.mark.asyncio
    async def test_sync_async_consistency(self, agent_store: AgentStore, sample_agent_data: dict[str, Any]):
        """Test that sync and async operations are consistent."""
        # Create agent synchronously
        sync_agent_id = agent_store.create_agent(
            name=f"sync_{sample_agent_data['name']}",
            definition=sample_agent_data["definition"]
        )

        # Create agent asynchronously
        async_agent_id = await agent_store.async_create_agent(
            name=f"async_{sample_agent_data['name']}",
            definition=sample_agent_data["definition"]
        )

        # Retrieve both agents using both methods
        sync_agent_sync_get = agent_store.get_agent(sync_agent_id)
        sync_agent_async_get = await agent_store.async_get_agent(sync_agent_id)
        async_agent_sync_get = agent_store.get_agent(async_agent_id)
        async_agent_async_get = await agent_store.async_get_agent(async_agent_id)

        # Verify consistency
        assert sync_agent_sync_get.definition == sync_agent_async_get.definition
        assert async_agent_sync_get.definition == async_agent_async_get.definition
        assert sync_agent_sync_get.definition == sample_agent_data["definition"]
        assert async_agent_async_get.definition == sample_agent_data["definition"]
