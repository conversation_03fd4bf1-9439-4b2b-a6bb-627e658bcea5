"""Tests for common.database module."""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from sqlalchemy.orm import Session

from common.database import get_db_session


class TestGetDbSession:
    """Test get_db_session function."""

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_creation_and_cleanup(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test session creation and cleanup."""
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        session_generator = get_db_session()
        session = next(session_generator)

        mock_create_engine.assert_called_once_with("postgresql://user:pass@localhost/test")
        mock_sessionmaker.assert_called_once_with(autocommit=False, autoflush=False, bind=mock_engine)
        assert session == mock_session

        # Test cleanup
        try:
            next(session_generator)
        except StopIteration:
            pass
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_exception_handling(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test session exception handling and cleanup."""
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        session_generator = get_db_session()
        session = next(session_generator)

        # Simulate exception during session usage
        try:
            session_generator.throw(SQLAlchemyError("Database error"))
        except SQLAlchemyError:
            pass

        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    def test_engine_creation_failure(self, mock_create_engine, mock_settings):
        """Test engine creation failure."""
        mock_settings.database.connection_string = "invalid://connection"
        mock_create_engine.side_effect = OperationalError("Connection failed", None, None)

        with pytest.raises(OperationalError):
            session_generator = get_db_session()
            next(session_generator)

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_configuration(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test session configuration parameters."""
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        session_generator = get_db_session()
        next(session_generator)

        mock_sessionmaker.assert_called_once_with(
            autocommit=False,
            autoflush=False,
            bind=mock_engine
        )
