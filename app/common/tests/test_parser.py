"""Tests for common.parser module."""

import json
import pytest

from common.parser import extract_and_parse_json


class TestExtractAndParseJson:
    """Test extract_and_parse_json function."""

    def test_plain_json_object(self):
        """Test parsing plain JSON object."""
        content = '{"name": "test", "value": 123}'
        result = extract_and_parse_json(content)
        assert result == {"name": "test", "value": 123}

    def test_plain_json_array(self):
        """Test parsing plain JSON array."""
        content = '[{"id": 1}, {"id": 2}]'
        result = extract_and_parse_json(content)
        assert result == [{"id": 1}, {"id": 2}]

    def test_markdown_code_block(self):
        """Test extracting JSON from markdown code block."""
        content = '```json\n{"name": "test", "value": 123}\n```'
        result = extract_and_parse_json(content)
        assert result == {"name": "test", "value": 123}

    def test_markdown_without_language(self):
        """Test extracting JSON from markdown without language specifier."""
        content = '```\n{"simple": "json"}\n```'
        result = extract_and_parse_json(content)
        assert result == {"simple": "json"}

    def test_json_with_header_text(self):
        """Test extracting JSON with header text."""
        content = 'Here is the result:\n{"data": "extracted", "success": true}'
        result = extract_and_parse_json(content)
        assert result == {"data": "extracted", "success": True}

    def test_nested_json_object(self):
        """Test parsing nested JSON object."""
        content = '''```json
{
    "user": {"name": "John", "age": 30},
    "preferences": ["json", "python"]
}
```'''
        result = extract_and_parse_json(content)
        expected = {
            "user": {"name": "John", "age": 30},
            "preferences": ["json", "python"]
        }
        assert result == expected

    def test_json_with_data_types(self):
        """Test parsing JSON with various data types."""
        content = '''```json
{
    "integer": 42,
    "float": 3.14,
    "boolean": true,
    "null_value": null
}
```'''
        result = extract_and_parse_json(content)
        assert result["integer"] == 42
        assert result["boolean"] is True
        assert result["null_value"] is None

    def test_invalid_json_raises_error(self):
        """Test that invalid JSON raises JSONDecodeError."""
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json('{"invalid": json}')

    def test_no_json_content_raises_error(self):
        """Test that content without JSON raises JSONDecodeError."""
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json("Just plain text with no JSON.")
