"""Test data-related fixtures."""

import uuid
from datetime import datetime, date
from typing import Dict, Any, List

import pytest


# Test data fixtures
@pytest.fixture
def sample_client_guid() -> uuid.UUID:
    """Sample client GUID for testing."""
    return uuid.UUID("12345678-1234-5678-9abc-123456789012")


@pytest.fixture
def sample_completion_request() -> Dict[str, Any]:
    """Sample CompletionRequest data for testing."""
    return {
        "model": "gpt-3.5-turbo",
        "messages": ["Test completion request"],
        "max_tokens": 100,
        "temperature": 0.7
    }


@pytest.fixture
def sample_chat_completion_request() -> Dict[str, Any]:
    """Sample ChatCompletionRequest data for testing."""
    return {
        "model": "gpt-4",
        "messages": [{"role": "user", "content": "Test chat message"}],
        "max_tokens": 150,
        "temperature": 0.8
    }


@pytest.fixture
def sample_user_guid() -> uuid.UUID:
    """Sample user GUID for testing."""
    return uuid.UUID("*************-8765-cba9-************")


# Additional model test fixtures
@pytest.fixture
def sample_job_ids() -> Dict[str, str]:
    """Sample job IDs for testing."""
    return {
        "uuid_job": "12345678-1234-5678-9abc-123456789012",
        "string_job": "batch-job-2023-10-01-001",
        "transcription_job": "transcribe-job-audio-001"
    }


@pytest.fixture
def sample_tag_usage_data() -> Dict[str, Any]:
    """Sample tag usage data for testing."""
    return {
        "tag": "production-api",
        "total_spend": 1250.75,
        "total_requests": 5000,
        "total_tokens": 250000,
        "success_rate": 99.8,
        "date_range": "2023-10-01 to 2023-10-07"
    }


@pytest.fixture
def sample_tag_detail_data() -> Dict[str, Any]:
    """Sample tag detail data for testing."""
    return {
        "id": uuid.uuid4(),
        "tag": "test-tag",
        "date": "2023-10-01",
        "api_key": "test-key",
        "model": "gpt-4",
        "model_group": "openai",
        "custom_llm_provider": "openai",
        "prompt_tokens": 100,
        "completion_tokens": 50,
        "cache_read_input_tokens": 0,
        "cache_creation_input_tokens": 0,
        "spend": 0.05,
        "api_requests": 1,
        "successful_requests": 1,
        "failed_requests": 0,
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }


@pytest.fixture
def sample_transcription_data() -> Dict[str, Any]:
    """Sample transcription request data for testing."""
    return {
        "s3_url": "s3://bucket/audio/meeting-2023-10-01.wav",
        "language_code": "en-US",
        "media_format": "wav",
        "user": "test_user_123"
    }


@pytest.fixture
def sample_batch_status_data() -> Dict[str, Any]:
    """Sample batch job status data for testing."""
    return {
        "job_id": "batch-job-status-test",
        "status": "IN_PROGRESS",
        "status_description": "Processing batch items",
        "progress": {"processed": 75, "total": 100, "percentage": 75.0},
        "created_at": "2023-10-01T10:00:00Z",
        "updated_at": "2023-10-01T10:30:00Z",
        "started_at": "2023-10-01T10:05:00Z",
        "completed_at": None
    }


@pytest.fixture
def sample_batch_result_data() -> Dict[str, Any]:
    """Sample batch job result data for testing."""
    return {
        "job_id": "batch-job-result-test",
        "status": "COMPLETED",
        "result_urls": [
            "s3://results/batch-job-result-test/output-1.json",
            "s3://results/batch-job-result-test/output-2.json"
        ],
        "pagination": {"page": 1, "per_page": 50, "total": 100, "pages": 2}
    }


@pytest.fixture
def invalid_uuid_strings() -> List[str]:
    """Invalid UUID strings for testing validation."""
    return [
        "invalid-uuid",
        "12345678-1234-5678-9abc",  # Too short
        "12345678-1234-5678-9abc-123456789012-extra",  # Too long
        "gggggggg-1234-5678-9abc-123456789012",  # Invalid characters
        "",  # Empty string
        "not-a-uuid-at-all"
    ]


@pytest.fixture
def edge_case_strings() -> Dict[str, str]:
    """Edge case strings for testing."""
    return {
        "empty": "",
        "whitespace": "   ",
        "unicode": "测试数据 🤖 émojis",
        "special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
        "very_long": "x" * 1000,
        "newlines": "line1\nline2\r\nline3",
        "tabs": "col1\tcol2\tcol3"
    }
