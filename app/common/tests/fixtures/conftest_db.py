"""Database-related test fixtures."""

import uuid
from typing import Any, Generator

import pytest
from llama_index.core.llms import ChatMessage
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from common.services import JobStore, PostgresChatStore, AgentStore, PromptStore
from common.services.job_store import BatchJobStatusEnum


# Database fixtures
# Database URL constants - used by session fixtures
TEST_DB_URL = "sqlite:///:memory:"
TEST_ASYNC_DB_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="function")
def db_session() -> Generator[Any, None, None]:
    """Create a test database session."""
    engine = create_engine(TEST_DB_URL, echo=False)
    session_local = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    try:
        yield session_local
    finally:
        engine.dispose()


@pytest.fixture(scope="function")
def async_db_session() -> Generator[Any, None, None]:
    """Create a test async database session."""
    engine = create_async_engine(TEST_ASYNC_DB_URL, echo=False)
    async_session_local = sessionmaker(
        bind=engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
    )
    try:
        yield async_session_local
    finally:
        engine.sync_engine.dispose()


# Service fixtures
@pytest.fixture
def job_store(db_session: Any, async_db_session: Any) -> JobStore:
    """Create a JobStore instance for testing."""
    return JobStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_batch_jobs",
        schema_name="public",
        use_jsonb=False,
    )


@pytest.fixture
def agent_store(db_session: Any, async_db_session: Any) -> AgentStore:
    """Create an AgentStore instance for testing."""
    return AgentStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_agents",
        schema_name="public",
    )


@pytest.fixture
def chat_store(db_session: Any, async_db_session: Any) -> PostgresChatStore:
    """Create a PostgresChatStore instance for testing."""
    return PostgresChatStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_chatstore",
        schema_name="public",
        use_jsonb=False,
    )


@pytest.fixture
def prompt_store(db_session: Any, async_db_session: Any) -> PromptStore:
    """Create a PromptStore instance for testing."""
    return PromptStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_prompts",
        schema_name="public",
    )


# Sample data fixtures
@pytest.fixture
def test_uuids() -> dict[str, uuid.UUID]:
    """Provide consistent UUIDs for testing."""
    return {
        "client_1": uuid.UUID("12345678-1234-5678-9abc-123456789abc"),
        "client_2": uuid.UUID("*************-8765-cba9-987654321cba"),
        "user_1": uuid.UUID("11111111-**************-************"),
        "user_2": uuid.UUID("*************-8888-9999-aaaaaaaaaaaa"),
        "agent_1": uuid.UUID("aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"),
        "job_1": uuid.UUID("ffffffff-0000-1111-2222-************"),
    }


@pytest.fixture
def sample_agent_data() -> dict[str, Any]:
    """Provide sample agent data for testing."""
    return {
        "name": "test_agent",
        "definition": {
            "type": "workflow",
            "steps": [
                {"action": "analyze", "parameters": {"depth": "deep"}},
                {"action": "generate", "parameters": {"format": "json"}},
            ],
            "metadata": {"version": "1.0", "author": "test_user"},
        },
    }


@pytest.fixture
def sample_chat_messages() -> list[ChatMessage]:
    """Provide sample chat messages for testing."""
    return [
        ChatMessage(role="user", content="Hello, how are you?"),
        ChatMessage(role="assistant", content="I'm doing well, thank you! How can I help you today?"),
        ChatMessage(role="user", content="Can you help me with a coding problem?"),
        ChatMessage(role="assistant", content="Of course! I'd be happy to help with your coding problem."),
    ]


@pytest.fixture
def sample_job_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample job data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "total_count": 10,
        "job_params": {
            "model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000,
            "batch_size": 5,
        },
        "status": BatchJobStatusEnum.PENDING,
    }


@pytest.fixture
def sample_prompt_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample prompt data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "prompt": "You are a helpful AI assistant. Please respond to user queries with accurate and helpful information.",
    }
