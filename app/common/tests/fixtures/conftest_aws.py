"""AWS and cloud service-related test fixtures."""

import os
from typing import Any, Generator

import boto3
import botocore.client
import pytest
from moto import mock_aws

from common.s3_storage import S3Storage

# Constants
DEFAULT_REGION = "us-east-1"
DEFAULT_BUCKET_NAME = "aca-dev-alpha-copilot-us-east-1"
DEFAULT_S3_PREFIX = "test-batch-jobs"

# Test data constants
TEST_JWT_KEY = "test-jwt-key"
TEST_JWT_ISSUER = "test-issuer"
TEST_JWT_AUDIENCE = "test-audience"


class MockProvider:
    """
    Provider for mocking AWS SSM requests.
    """

    def _ssm_setup(self) -> None:
        """
        Mocks param store setup with better error handling.
        """
        try:
            region_name = os.environ.get("AWS_REGION", DEFAULT_REGION)
            self.ssm_client = boto3.client("ssm", region_name=region_name)

            parameters = [
                ("/alpha/test/global/Web/Jwt/Key", os.environ.get("JWT_KEY", TEST_JWT_KEY), "SecureString"),
                ("/alpha/test/global/Web/Jwt/Issuer", os.environ.get("JWT_ISSUER", TEST_JWT_ISSUER), "String"),
                ("/alpha/test/global/Web/Jwt/Audience", os.environ.get("JWT_AUDIENCE", TEST_JWT_AUDIENCE), "String"),
            ]

            for name, value, param_type in parameters:
                # Type ignore for boto3 parameter type issue
                self.ssm_client.put_parameter(
                    Name=name,
                    Value=value,
                    Type=param_type  # type: ignore[arg-type]
                )

            assert isinstance(self.ssm_client, botocore.client.BaseClient)
            assert self.ssm_client.meta.service_model.service_name == "ssm"

        except Exception as e:
            pytest.fail(f"Failed to set up SSM mock: {e}")

    def set_up_mock(self) -> None:
        """
        Mocks param store setup.
        """
        self._ssm_setup()


# AWS and S3 fixtures
@pytest.fixture
def s3_setup() -> Generator[Any, None, None]:
    """Set up mocked S3 for testing."""
    with mock_aws():
        client = boto3.client("s3", region_name=DEFAULT_REGION)  # type: ignore[misc]
        client.create_bucket(Bucket=DEFAULT_BUCKET_NAME)
        client.put_object(Bucket=DEFAULT_BUCKET_NAME, Key="test_data/test-docx.pdf", Body=b"Test PDF Content")
        client.put_object(Bucket=DEFAULT_BUCKET_NAME, Key="test_data/test-pptx.pdf", Body=b"Test PPTX Content")
        yield client


@pytest.fixture
def s3_storage(s3_setup: Any) -> S3Storage:
    """Create an S3Storage instance for testing."""
    return S3Storage(
        bucket_name=DEFAULT_BUCKET_NAME,
        prefix=DEFAULT_S3_PREFIX
    )


# Environment setup
@pytest.fixture(autouse=True)
def setup_test_environment() -> Generator[None, None, None]:
    """Set up test environment variables."""
    test_env = {
        "AWS_REGION": DEFAULT_REGION,
        "env_name": "test",
        "jwt_key": TEST_JWT_KEY,
        "jwt_issuer": TEST_JWT_ISSUER,
        "jwt_audience": TEST_JWT_AUDIENCE,
    }

    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value

    yield

    # Clean up environment variables
    for key in test_env.keys():
        os.environ.pop(key, None)
