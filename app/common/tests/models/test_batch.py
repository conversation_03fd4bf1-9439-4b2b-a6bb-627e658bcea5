"""Tests for common.models.batch module."""

import uuid

import pytest
from pydantic import ValidationError

from common.models.batch import JobResponse, JobStatusResponse, JobResultResponse


class TestJobResponse:
    """Test JobResponse model."""

    def test_valid_creation(self) -> None:
        """Test creating JobResponse with valid UUID."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, uuid.UUID)

    def test_string_uuid_conversion(self) -> None:
        """Test JobResponse accepts string UUID."""
        job_id_str = "12345678-1234-5678-9abc-123456789012"
        response = JobResponse(job_id=job_id_str)

        assert response.job_id == uuid.UUID(job_id_str)
        assert isinstance(response.job_id, uuid.UUID)

    def test_validation_errors(self) -> None:
        """Test JobResponse validation failures."""
        # Test invalid UUID string
        with pytest.raises(ValidationError, match=r"Input should be a valid UUID"):
            JobResponse(job_id="invalid-uuid")

        # Test missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResponse()

    def test_json_serialization(self) -> None:
        """Test JobResponse JSON serialization."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test dict serialization - UUID remains as UUID object in model_dump()
        json_dict = response.model_dump()
        assert json_dict["job_id"] == job_id

        # Test JSON string serialization - UUID is converted to string
        json_str = response.model_dump_json()
        assert str(job_id) in json_str


class TestJobStatusResponse:
    """Test JobStatusResponse model."""

    def test_minimal_creation(self) -> None:
        """Test creating JobStatusResponse with required fields only."""
        response = JobStatusResponse(job_id="test-job", status="PENDING")

        assert response.job_id == "test-job"
        assert response.status == "PENDING"
        assert response.progress == {}

    def test_with_optional_fields(self) -> None:
        """Test JobStatusResponse with optional fields."""
        response = JobStatusResponse(
            job_id="test-job",
            status="IN_PROGRESS",
            status_description="Processing items",
            progress={"processed": 50, "total": 100}
        )

        assert response.job_id == "test-job"
        assert response.status == "IN_PROGRESS"
        assert response.status_description == "Processing items"
        assert response.progress == {"processed": 50, "total": 100}

    def test_validation_errors(self) -> None:
        """Test JobStatusResponse validation failures."""
        # Missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobStatusResponse(status="PENDING")

        # Missing status
        with pytest.raises(ValidationError, match=r"Field required"):
            JobStatusResponse(job_id="test-job")

    def test_json_serialization(self) -> None:
        """Test JobStatusResponse JSON serialization."""
        response = JobStatusResponse(
            job_id="test-job",
            status="COMPLETED",
            progress={"processed": 100, "total": 100}
        )

        json_dict = response.model_dump()
        assert json_dict["job_id"] == "test-job"
        assert json_dict["status"] == "COMPLETED"
        assert json_dict["progress"] == {"processed": 100, "total": 100}


class TestJobResultResponse:
    """Test JobResultResponse model."""

    def test_minimal_creation(self) -> None:
        """Test creating JobResultResponse with required fields only."""
        response = JobResultResponse(job_id="test-job", status="COMPLETED")

        assert response.job_id == "test-job"
        assert response.status == "COMPLETED"
        assert response.result_urls == []
        assert response.pagination == {}  # default_factory=dict means empty dict, not None

    def test_with_optional_fields(self) -> None:
        """Test JobResultResponse with optional fields."""
        response = JobResultResponse(
            job_id="test-job",
            status="COMPLETED",
            result_urls=["s3://bucket/result1.json", "s3://bucket/result2.json"],
            pagination={"page": 1, "total": 100}
        )

        assert response.job_id == "test-job"
        assert response.status == "COMPLETED"
        assert len(response.result_urls) == 2
        assert response.pagination == {"page": 1, "total": 100}

    def test_validation_errors(self) -> None:
        """Test JobResultResponse validation failures."""
        # Missing status
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResultResponse(job_id="test")

        # Missing job_id
        with pytest.raises(ValidationError, match=r"Field required"):
            JobResultResponse(status="COMPLETED")

    def test_json_serialization(self) -> None:
        """Test JobResultResponse JSON serialization."""
        response = JobResultResponse(
            job_id="test-job",
            status="COMPLETED",
            result_urls=["s3://bucket/result.json"]
        )

        json_dict = response.model_dump()
        assert json_dict["job_id"] == "test-job"
        assert json_dict["status"] == "COMPLETED"
        assert json_dict["result_urls"] == ["s3://bucket/result.json"]


class TestBatchModelsIntegration:
    """Integration tests for batch models."""

    def test_realistic_workflow(self) -> None:
        """Test realistic batch job workflow."""
        # 1. Job submission
        job_id = uuid.uuid4()
        submission_response = JobResponse(job_id=job_id)

        # 2. Job status check
        status_response = JobStatusResponse(
            job_id=str(job_id),
            status="IN_PROGRESS",
            progress={"processed": 50, "total": 100}
        )

        # 3. Job completion
        result_response = JobResultResponse(
            job_id=str(job_id),
            status="COMPLETED",
            result_urls=["s3://results/output.json"]
        )

        # Verify workflow consistency
        assert str(submission_response.job_id) == status_response.job_id
        assert status_response.job_id == result_response.job_id

    def test_serialization_consistency(self) -> None:
        """Test serialization consistency across models."""
        job_id = uuid.uuid4()

        job_response = JobResponse(job_id=job_id)
        status_response = JobStatusResponse(job_id=str(job_id), status="COMPLETED")
        result_response = JobResultResponse(job_id=str(job_id), status="COMPLETED")

        # All should serialize to dicts
        job_json = job_response.model_dump()
        status_json = status_response.model_dump()
        result_json = result_response.model_dump()

        assert isinstance(job_json, dict)
        assert isinstance(status_json, dict)
        assert isinstance(result_json, dict)
        assert "job_id" in job_json
        assert "job_id" in status_json
        assert "job_id" in result_json
