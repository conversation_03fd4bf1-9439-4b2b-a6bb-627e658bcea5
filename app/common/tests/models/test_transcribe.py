"""Concise tests for common.models.transcribe module."""

import io

import pytest
from fastapi import UploadFile
from pydantic import ValidationError

from common.models.transcribe import TranscriptionRequest, TranscriptionJobResponse


class TestTranscriptionRequest:
    """Test cases for TranscriptionRequest model."""

    def test_with_upload_file(self) -> None:
        """Test TranscriptionRequest with UploadFile."""
        upload_file = UploadFile(
            filename="test_audio.mp3",
            file=io.BytesIO(b"mock audio content")
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code="en-US",
            media_format="mp3"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "mp3"

    def test_with_string_url(self) -> None:
        """Test TranscriptionRequest with S3 URL string."""
        s3_url = "s3://bucket/audio/meeting.wav"
        request = TranscriptionRequest(
            file=s3_url,
            language_code="en-US",
            media_format="wav",
            user="test_user"
        )

        assert request.file == s3_url
        assert request.language_code == "en-US"
        assert request.media_format == "wav"
        assert request.user == "test_user"

    def test_minimal_fields(self) -> None:
        """Test TranscriptionRequest with only required field."""
        s3_url = "s3://bucket/audio/file.mp3"
        request = TranscriptionRequest(file=s3_url)

        assert request.file == s3_url
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_missing_file_validation(self) -> None:
        """Test TranscriptionRequest validation without file."""
        with pytest.raises(ValidationError, match=r"Field required"):
            TranscriptionRequest(language_code="en-US", user="test")

    def test_json_serialization(self) -> None:
        """Test TranscriptionRequest JSON serialization."""
        request = TranscriptionRequest(
            file="s3://bucket/audio.wav",
            language_code="en-US",
            media_format="wav"
        )

        json_data = request.model_dump()
        assert json_data["file"] == "s3://bucket/audio.wav"
        assert json_data["language_code"] == "en-US"
        assert json_data["media_format"] == "wav"

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "user" not in json_data_exclude_none

    def test_as_form_method(self) -> None:
        """Test TranscriptionRequest.as_form class method."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"audio content")
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"

    def test_as_form_with_url(self) -> None:
        """Test as_form with file_url parameter."""
        file_url = "s3://bucket/audio.mp3"

        request = TranscriptionRequest.as_form(
            file_url=file_url,
            language_code="es-ES"
        )

        assert request.file == file_url
        assert request.language_code == "es-ES"

    def test_as_form_validation_error(self) -> None:
        """Test as_form validation when both file and file_url are missing."""
        with pytest.raises(ValueError, match=r"file or file_url must be provided"):
            TranscriptionRequest.as_form(language_code="en-US")


class TestTranscriptionJobResponse:
    """Test cases for TranscriptionJobResponse model."""

    def test_creation(self) -> None:
        """Test creating TranscriptionJobResponse."""
        job_id = "transcribe-job-audio-001"
        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, str)

    def test_missing_job_id_validation(self) -> None:
        """Test TranscriptionJobResponse validation without job_id."""
        with pytest.raises(ValidationError, match=r"Field required"):
            TranscriptionJobResponse()

    def test_json_serialization(self) -> None:
        """Test TranscriptionJobResponse JSON serialization."""
        job_id = "transcribe-job-audio-001"
        response = TranscriptionJobResponse(job_id=job_id)

        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

        json_str = response.model_dump_json()
        assert job_id in json_str

    def test_equality(self) -> None:
        """Test TranscriptionJobResponse equality comparison."""
        job_id = "same-job-id"
        response1 = TranscriptionJobResponse(job_id=job_id)
        response2 = TranscriptionJobResponse(job_id=job_id)
        response3 = TranscriptionJobResponse(job_id="different-job-id")

        assert response1 == response2
        assert response1 != response3


class TestTranscribeModelsIntegration:
    """Integration tests for transcribe models."""

    def test_request_response_workflow(self) -> None:
        """Test realistic transcription workflow."""
        # Create request
        request = TranscriptionRequest(
            file="s3://bucket/audio/meeting.wav",
            language_code="en-US",
            media_format="wav",
            user="test_user"
        )

        # Simulate job submission response
        job_id = "transcription_job_2023_10_01_001"
        response = TranscriptionJobResponse(job_id=job_id)

        # Verify data consistency
        assert request.file == "s3://bucket/audio/meeting.wav"
        assert request.language_code == "en-US"
        assert response.job_id == job_id

    def test_form_submission_workflow(self) -> None:
        """Test form-based transcription workflow."""
        # Simulate form submission with file upload
        upload_file = UploadFile(
            filename="meeting_recording.wav",
            file=io.BytesIO(b"mock audio data")
        )

        # Create request using as_form method
        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US"
        )

        # Verify request creation
        assert request.file == upload_file
        assert request.language_code == "en-US"

        # Simulate job creation
        response = TranscriptionJobResponse(job_id="transcribe-meeting-001")
        assert response.job_id == "transcribe-meeting-001"

    def test_serialization_consistency(self) -> None:
        """Test serialization consistency across models."""
        # Create request
        request = TranscriptionRequest(
            file="s3://bucket/audio.wav",
            language_code="en-US"
        )

        # Create response
        response = TranscriptionJobResponse(job_id="test-job-123")

        # Test serialization
        request_json = request.model_dump()
        response_json = response.model_dump()

        # Verify both serialize to dicts
        assert isinstance(request_json, dict)
        assert isinstance(response_json, dict)
        assert "file" in request_json
        assert "job_id" in response_json
