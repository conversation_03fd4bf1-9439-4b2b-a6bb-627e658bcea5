"""Concise tests for common.models.tags module."""

import uuid
from datetime import datetime

import pytest
from pydantic import ValidationError

from common.models.tags import (
    LiteLLMDailyTagSpend,
    TagUsageSummary,
    TagUsageDetail,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageResponse
)


class TestLiteLLMDailyTagSpend:
    """Test cases for LiteLLMDailyTagSpend SQLAlchemy model."""

    def test_table_structure(self) -> None:
        """Test SQLAlchemy table structure."""
        model = LiteLLMDailyTagSpend

        # Test table configuration
        assert model.__tablename__ == "LiteLLM_DailyTagSpend"
        assert model.__table_args__["schema"] == "public"

        # Verify it's a proper SQLAlchemy model
        assert hasattr(model, '__table__')
        assert hasattr(model, '__mapper__')

        # Verify expected columns exist
        table = model.__table__
        column_names = [col.name for col in table.columns]
        expected_columns = ["date", "tag", "spend", "api_requests", "prompt_tokens", "completion_tokens"]
        for expected_col in expected_columns:
            assert expected_col in column_names, f"Table should have column: {expected_col}"


class TestTagUsageSummary:
    """Test cases for TagUsageSummary model."""

    def test_creation(self) -> None:
        """Test creating TagUsageSummary with valid data."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=95.5,
            date_range="2023-10-01"
        )

        assert summary.tag == "test-tag"
        assert summary.total_spend == 100.0
        assert summary.total_requests == 1000
        assert summary.total_tokens == 50000
        assert summary.success_rate == 95.5
        assert summary.date_range == "2023-10-01"

    def test_missing_required_fields(self) -> None:
        """Test TagUsageSummary validation with missing required fields."""
        with pytest.raises(ValidationError, match=r"Field required"):
            TagUsageSummary(total_spend=100.0)  # Missing tag

    def test_json_serialization(self) -> None:
        """Test TagUsageSummary JSON serialization."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=95.5,
            date_range="2023-10-01"
        )

        json_dict = summary.model_dump()
        assert json_dict["tag"] == "test-tag"
        assert json_dict["total_spend"] == 100.0
        assert json_dict["total_requests"] == 1000


class TestTagUsageDetail:
    """Test cases for TagUsageDetail model."""

    def test_creation(self) -> None:
        """Test creating TagUsageDetail with valid data."""
        detail = TagUsageDetail(
            id=uuid.uuid4(),
            tag="test-tag",
            date="2023-10-01",
            api_key="test-key",
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=100,
            completion_tokens=50,
            cache_read_input_tokens=0,
            cache_creation_input_tokens=0,
            spend=0.05,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        assert isinstance(detail.id, uuid.UUID)
        assert detail.tag == "test-tag"
        assert detail.model == "gpt-4"
        assert detail.spend == 0.05


class TestTagUsageByDate:
    """Test cases for TagUsageByDate model."""

    def test_creation(self) -> None:
        """Test creating TagUsageByDate with valid data."""
        usage = TagUsageByDate(
            date="2023-10-01",
            spend=125.50,
            api_requests=500,
            total_tokens=25000,
            success_rate=99.2
        )

        assert usage.date == "2023-10-01"
        assert usage.spend == 125.50
        assert usage.api_requests == 500


class TestTagUsageByModel:
    """Test cases for TagUsageByModel model."""

    def test_creation(self) -> None:
        """Test creating TagUsageByModel with valid data."""
        usage = TagUsageByModel(
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=875.52,
            total_requests=3500,
            total_tokens=175000,
            success_rate=99.9
        )

        assert usage.model == "gpt-4"
        assert usage.total_spend == 875.52
        assert usage.total_requests == 3500


class TestTagUsageResponse:
    """Test cases for TagUsageResponse model."""

    def test_creation(self) -> None:
        """Test creating TagUsageResponse with all components."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=100,
            total_tokens=10000,
            success_rate=99.0,
            date_range="2023-10-01"
        )

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=100
        )

        assert response.tag == "test-tag"
        assert response.summary == summary
        assert response.total_records == 100

    def test_json_serialization(self) -> None:
        """Test TagUsageResponse JSON serialization."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=100.0,
            total_requests=100,
            total_tokens=10000,
            success_rate=99.0,
            date_range="2023-10-01"
        )

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=100
        )

        json_data = response.model_dump()
        assert json_data["tag"] == "test-tag"
        assert "summary" in json_data
        assert json_data["total_records"] == 100
