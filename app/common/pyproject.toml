[project]
name = "common"
version = "0.1.0"
description = "Shared utilities"
requires-python = ">=3.11"
dependencies = [
    "encore-ai",
    "asyncpg>=0.30.0",
    "boto3>=1.34.0",
    "psycopg>=3.2.9",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.10.1",
]

[tool.uv.sources]
encore-ai = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]
