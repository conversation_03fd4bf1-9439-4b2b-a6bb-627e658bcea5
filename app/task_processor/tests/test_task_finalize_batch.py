import uuid
from unittest import mock

from common.enums import BatchJobStatusEnum
from task_processor.tasks import task_finalize_job


class TestTaskFinalizeBatch:
    def setup_method(self):
        self.job_id = str(uuid.uuid4())
        self.job_id_obj = uuid.UUID(self.job_id)

        self.callback_url = "https://example.com/callback"
        self.job_params = {
            "prompt": "Test prompt",
            "location": "https://example.com/data.json",
            "callback_url": self.callback_url,
        }

        self.job_data = {
            "id": self.job_id,
            "job_params": self.job_params,
            "status": BatchJobStatusEnum.PROCESSING.value,
        }

        self.mock_get_batch_job_manager = mock.patch("task_processor.tasks.generate.get_batch_job_manager").start()

        self.mock_send_callback = mock.patch("task_processor.tasks.generate.send_callback").start()

        self.mock_batch_job = mock.MagicMock()
        self.mock_get_batch_job_manager.return_value = self.mock_batch_job
        self.mock_batch_job.get_job.return_value = self.job_data

    def teardown_method(self):
        mock.patch.stopall()

    def test_successful_finalization(self):
        task_finalize_job(self.job_id, True)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)

        self.mock_batch_job.update_job.assert_called_once()
        args, kwargs = self.mock_batch_job.update_job.call_args
        assert kwargs["job_id"] == self.job_id_obj
        assert "status" in kwargs
        assert "status_description" not in kwargs
        assert "completed_at" in kwargs

    def test_failed_finalization(self):
        task_finalize_job(self.job_id, False)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)

        self.mock_batch_job.update_job.assert_called_once()
        args, kwargs = self.mock_batch_job.update_job.call_args
        assert kwargs["job_id"] == self.job_id_obj
        assert "completed_at" in kwargs

    def test_job_not_found(self):
        self.mock_batch_job.get_job.return_value = None

        task_finalize_job(self.job_id, True)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)

        self.mock_batch_job.update_job.assert_not_called()
        self.mock_send_callback.assert_not_called()

    def test_job_already_completed(self):
        completed_job = self.job_data.copy()
        completed_job["status"] = BatchJobStatusEnum.COMPLETED.value
        self.mock_batch_job.get_job.return_value = completed_job

        task_finalize_job(self.job_id, True)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)

        self.mock_batch_job.update_job.assert_not_called()
        self.mock_send_callback.assert_not_called()

    def test_no_callback_url(self):
        job_data = self.job_data.copy()
        job_data["job_params"] = {"prompt": "Test prompt"}
        self.mock_batch_job.get_job.return_value = job_data

        task_finalize_job(self.job_id, True)

        self.mock_batch_job.update_job.assert_called_once()
        self.mock_send_callback.assert_not_called()
