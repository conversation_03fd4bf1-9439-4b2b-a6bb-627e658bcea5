import asyncio
import uuid
from unittest import mock

import pytest
from task_processor.tasks import task_generate


class TestTaskGenerate:
    def setup_method(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.job_id = str(uuid.uuid4())
        self.job_id_obj = uuid.UUID(self.job_id)

        self.request_data = {
            "prompt": "Test prompt",
            "context": "Test context",
            "model": "test-model",
            "temperature": 0.7,
            "top_p": 0.9,
            "output_type": "text",
            "client_guid": "client123",
            "user_guid": "user456",
        }

        self.llm_result = {"text": "Generated response", "score": 0.95}

        # Mock S3 storage
        self.mock_get_s3_storage = mock.patch("task_processor.tasks.generate.get_s3_storage_for_job_results").start()
        self.mock_s3_instance = mock.MagicMock()
        self.mock_get_s3_storage.return_value = self.mock_s3_instance

        # Mock task finalize
        self.mock_task_finalize_job = mock.patch("task_processor.tasks.generate.task_finalize_job").start()

        # Mock task check
        self.mock_task_check_job = mock.patch("task_processor.tasks.generate.task_check_job").start()

        # Mock LLM service
        self.mock_llm_service = mock.patch("task_processor.tasks.generate.LlmService").start()
        self.mock_llm_instance = mock.MagicMock()
        self.mock_llm_service.return_value = self.mock_llm_instance

        # mock task_upload_result_to_s3
        self.mock_task_upload_result_to_s3 = mock.patch("task_processor.tasks.generate.task_upload_result_to_s3").start()
        self.mock_task_upload_result_to_s3 = mock.MagicMock()

        async def mock_generate(*args, **kwargs):
            return self.llm_result

        self.mock_llm_instance.generate.side_effect = mock_generate

    def teardown_method(self):
        mock.patch.stopall()
        self.loop.close()

    def test_successful_generation(self):
        task_generate(self.request_data)

        self.mock_llm_service.assert_called_once()

        self.mock_llm_instance.generate.assert_called_once_with(
            prompt="Test prompt",
            context="Test context",
            model="test-model",
            temperature=0.7,
            top_p=0.9,
            output_type="text",
        )

        self.mock_task_upload_result_to_s3.send.assert_not_called()
        self.mock_task_check_job.send.assert_not_called()

    def test_successful_generation_with_job_id(self):
        request_data = self.request_data.copy()
        request_data["job_id"] = self.job_id

        task_generate(request_data)

        self.mock_llm_instance.generate.assert_called_once()

    def test_missing_prompt_or_context(self):
        request_data = self.request_data.copy()
        request_data["prompt"] = ""

        with pytest.raises(ValueError):
            task_generate(request_data)

        self.mock_llm_instance.generate.assert_not_called()
        self.mock_s3_instance.store_result.assert_not_called()
        self.mock_task_check_job.send.assert_not_called()

    def test_llm_generation_exception(self):
        self.mock_llm_instance.generate.side_effect = Exception("LLM error")

        with pytest.raises(Exception):
            task_generate(self.request_data)

        self.mock_s3_instance.store_result.assert_not_called()
        self.mock_task_check_job.send.assert_not_called()

    def test_s3_storage_exception(self):
        self.mock_s3_instance.store_result.side_effect = Exception("S3 error")

        task_generate(self.request_data)

        self.mock_task_check_job.send.assert_not_called()

    def test_different_output_types(self):
        request_data = self.request_data.copy()
        request_data["output_type"] = "json"
        self.llm_result = {"json": {"key": "value"}}

        task_generate(request_data)

        self.mock_llm_instance.generate.assert_called_once_with(
            prompt="Test prompt",
            context="Test context",
            model="test-model",
            temperature=0.7,
            top_p=0.9,
            output_type="json",
        )

    def test_no_job_id(self):
        request_data = self.request_data.copy()
        request_data.pop("job_id", None)

        task_generate(request_data)

        self.mock_llm_instance.generate.assert_called_once()
        self.mock_s3_instance.store_result.assert_not_called()
        self.mock_task_finalize_job.send.assert_not_called()
