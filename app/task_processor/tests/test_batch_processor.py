import json
from unittest.mock import MagicMock, patch

import pytest
from task_processor import batch_processor


class TestGetBatchJobManager:
    @patch("task_processor.batch_processor.BatchJob")
    @patch("task_processor.batch_processor.settings")
    def test_get_batch_job_manager(self, mock_settings, mock_batch_job):
        # Configure the mock settings
        mock_settings.database.connection_string = "mock_connection_string"
        mock_settings.database.db_name = "mock_db_name"

        # Configure the mock BatchJob.from_uri to return a mock instance
        mock_batch_job_instance = MagicMock()
        mock_batch_job.from_uri.return_value = mock_batch_job_instance

        # Call the function
        result = batch_processor.get_batch_job_manager()

        # Verify that BatchJob.from_uri was called with the expected arguments
        mock_batch_job.from_uri.assert_called_once_with(
            uri="mock_connection_string",
            db_name="mock_db_name",
            table_name="batch_jobs",
        )

        # Verify that the function returns the expected result
        assert result == mock_batch_job_instance


class TestValidateData:
    def test_validate_data_valid_input(self):
        # Valid input data
        data = [{"context": {"key": "value"}}, {"context": {"another_key": "another_value"}}]

        # Should not raise an exception
        try:
            batch_processor._validate_data(data)
        except ValueError:
            pytest.fail("_validate_data raised ValueError unexpectedly!")

    def test_validate_data_not_a_list(self):
        # Invalid input: not a list
        data = {"not": "a list"}

        # Should raise ValueError
        with pytest.raises(ValueError, match="Input data must be a list of context dictionaries"):
            batch_processor._validate_data(data)

    def test_validate_data_missing_context(self):
        # Invalid input: missing 'context' key
        data = [{"context": {"key": "value"}}, {"missing_context_key": "value"}]

        # Should raise ValueError
        with pytest.raises(ValueError, match="Each item must be a dictionary with a 'context' key"):
            batch_processor._validate_data(data)

    def test_validate_data_not_a_dict(self):
        # Invalid input: not a dictionary
        data = [{"context": {"key": "value"}}, "not a dict"]

        # Should raise ValueError
        with pytest.raises(ValueError, match="Each item must be a dictionary with a 'context' key"):
            batch_processor._validate_data(data)


class TestFetchInputData:
    @patch("task_processor.batch_processor.httpx")
    def test_fetch_input_data_from_url(self, mock_httpx):
        # Configure the mock response
        mock_response = MagicMock()
        mock_response.json.return_value = [{"context": {"key": "value"}}]
        mock_httpx.get.return_value = mock_response

        # URL to test
        url = "https://example.com/data.json"

        # Call the function
        result = batch_processor.fetch_input_data(url)

        # Verify the HTTP request was made and parsed correctly
        mock_httpx.get.assert_called_once_with(url)
        mock_response.raise_for_status.assert_called_once()
        mock_response.json.assert_called_once()

        # Verify the result
        assert result == {url: [{"context": {"key": "value"}}]}

    @patch("task_processor.batch_processor.boto3")
    def test_fetch_input_data_from_s3_file(self, mock_boto3):
        # Configure the mock S3 client and response
        mock_s3_client = MagicMock()
        mock_boto3.client.return_value = mock_s3_client

        # Mock the S3 get_object response
        mock_body = MagicMock()
        mock_body.read.return_value = json.dumps([{"context": {"key": "value"}}])
        mock_s3_client.get_object.return_value = {"Body": mock_body}

        # S3 path to test
        s3_path = "s3://my-bucket/path/to/file.json"

        # Call the function
        result = batch_processor.fetch_input_data(s3_path)

        # Verify the S3 client was created and get_object was called
        mock_boto3.client.assert_called_once_with("s3")
        mock_s3_client.get_object.assert_called_once_with(Bucket="my-bucket", Key="path/to/file.json")

        # Verify the result
        assert result == {"path/to/file.json": [{"context": {"key": "value"}}]}

    @patch("task_processor.batch_processor.boto3")
    def test_fetch_input_data_from_s3_directory(self, mock_boto3):
        # Configure the mock S3 client
        mock_s3_client = MagicMock()
        mock_boto3.client.return_value = mock_s3_client

        # Mock the paginator and responses
        mock_paginator = MagicMock()
        mock_s3_client.get_paginator.return_value = mock_paginator

        # Create mock pages
        mock_page = {
            "Contents": [
                {"Key": "path/to/dir/file1.json"},
                {"Key": "path/to/dir/file2.json"},
                {"Key": "path/to/dir/not_json.txt"},
            ]
        }
        mock_paginator.paginate.return_value = [mock_page]

        # Mock the get_object responses
        def mock_get_object(Bucket, Key):
            if Key == "path/to/dir/file1.json":
                mock_body = MagicMock()
                mock_body.read.return_value = json.dumps([{"context": {"file": "file1"}}])
                return {"Body": mock_body}
            elif Key == "path/to/dir/file2.json":
                mock_body = MagicMock()
                mock_body.read.return_value = json.dumps([{"context": {"file": "file2"}}])
                return {"Body": mock_body}
            return None

        mock_s3_client.get_object.side_effect = mock_get_object

        # S3 directory path to test
        s3_directory = "s3://my-bucket/path/to/dir/"

        # Call the function
        result = batch_processor.fetch_input_data(s3_directory)

        # Verify the S3 client was created and paginator was used
        mock_boto3.client.assert_called_once_with("s3")
        mock_s3_client.get_paginator.assert_called_once_with("list_objects_v2")
        mock_paginator.paginate.assert_called_once_with(Bucket="my-bucket", Prefix="path/to/dir/")

        # Verify get_object was called for both JSON files
        assert mock_s3_client.get_object.call_count == 2

        # Verify the result contains both files
        assert len(result) == 2
        assert "path/to/dir/file1.json" in result
        assert "path/to/dir/file2.json" in result
        assert result["path/to/dir/file1.json"] == [{"context": {"file": "file1"}}]
        assert result["path/to/dir/file2.json"] == [{"context": {"file": "file2"}}]

    @patch("task_processor.batch_processor.boto3")
    def test_fetch_input_data_from_s3_directory_no_valid_files(self, mock_boto3):
        # Configure the mock S3 client
        mock_s3_client = MagicMock()
        mock_boto3.client.return_value = mock_s3_client

        # Mock the paginator and responses
        mock_paginator = MagicMock()
        mock_s3_client.get_paginator.return_value = mock_paginator

        # Create mock page with no JSON files
        mock_page = {"Contents": [{"Key": "path/to/dir/not_json1.txt"}, {"Key": "path/to/dir/not_json2.txt"}]}
        mock_paginator.paginate.return_value = [mock_page]

        # S3 directory path to test
        s3_directory = "s3://my-bucket/path/to/dir/"

        # Should raise ValueError
        with pytest.raises(ValueError, match="No valid JSON files found in the S3 directory."):
            batch_processor.fetch_input_data(s3_directory)

    @patch("task_processor.batch_processor.boto3")
    def test_fetch_input_data_from_s3_directory_with_invalid_json(self, mock_boto3):
        # Configure the mock S3 client
        mock_s3_client = MagicMock()
        mock_boto3.client.return_value = mock_s3_client

        # Mock the paginator and responses
        mock_paginator = MagicMock()
        mock_s3_client.get_paginator.return_value = mock_paginator

        # Create mock page
        mock_page = {"Contents": [{"Key": "path/to/dir/valid.json"}, {"Key": "path/to/dir/invalid.json"}]}
        mock_paginator.paginate.return_value = [mock_page]

        # Mock the get_object responses
        def mock_get_object(Bucket, Key):
            if Key == "path/to/dir/valid.json":
                mock_body = MagicMock()
                mock_body.read.return_value = json.dumps([{"context": {"file": "valid"}}])
                return {"Body": mock_body}
            elif Key == "path/to/dir/invalid.json":
                # This file has invalid structure (missing 'context' key)
                mock_body = MagicMock()
                mock_body.read.return_value = json.dumps([{"invalid": "structure"}])
                return {"Body": mock_body}
            return None

        mock_s3_client.get_object.side_effect = mock_get_object

        # S3 directory path to test
        s3_directory = "s3://my-bucket/path/to/dir/"

        # Call the function
        result = batch_processor.fetch_input_data(s3_directory)

        # Verify both files were retrieved but only valid one made it to results
        assert mock_s3_client.get_object.call_count == 2
        assert len(result) == 2
        assert "path/to/dir/valid.json" in result
        assert "path/to/dir/invalid.json" in result

    def test_fetch_input_data_empty_location(self):
        # Test with empty location
        with pytest.raises(ValueError, match="Location must be provided and not be empty or whitespace"):
            batch_processor.fetch_input_data("")

    def test_fetch_input_data_whitespace_location(self):
        # Test with whitespace-only location
        with pytest.raises(ValueError, match="Location must be provided and not be empty or whitespace"):
            batch_processor.fetch_input_data("   ")

    def test_fetch_input_data_unsupported_format(self):
        # Test with unsupported format
        with pytest.raises(ValueError, match="Unsupported location format. Must be a URL or S3 path."):
            batch_processor.fetch_input_data("/local/path/file.json")
