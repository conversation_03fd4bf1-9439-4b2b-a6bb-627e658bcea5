import uuid
from unittest import mock

import httpx
import pytest
from task_processor.utils import send_callback


class TestSendCallback:
    @mock.patch("httpx.post")
    def test_successful_callback(self, mock_post):
        mock_response = mock.MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        callback_url = "https://example.com/callback"
        data = {"status": "completed", "job_id": str(uuid.uuid4())}

        send_callback(callback_url, data)
        mock_post.assert_called_once_with(callback_url, json=data, timeout=10.0)
        mock_response.raise_for_status.assert_called_once()

    @mock.patch("httpx.post")
    def test_failed_callback_http_error(self, mock_post):
        mock_post.side_effect = httpx.HTTPError("HTTP Error")

        callback_url = "https://example.com/callback"
        data = {"status": "completed", "job_id": str(uuid.uuid4())}
        with pytest.raises(httpx.HTTPError):
            send_callback(callback_url, data)
        mock_post.assert_called_once_with(callback_url, json=data, timeout=10.0)

    @mock.patch("httpx.post")
    def test_failed_callback_timeout(self, mock_post):
        mock_post.side_effect = httpx.TimeoutException("Timeout")

        callback_url = "https://example.com/callback"
        data = {"status": "completed", "job_id": str(uuid.uuid4())}
        with pytest.raises(httpx.TimeoutException):
            send_callback(callback_url, data)
        mock_post.assert_called_once_with(callback_url, json=data, timeout=10.0)

    @mock.patch("httpx.post")
    def test_failed_callback_general_exception(self, mock_post):
        mock_post.side_effect = Exception("General error")

        callback_url = "https://example.com/callback"
        data = {"status": "completed", "job_id": str(uuid.uuid4())}
        with pytest.raises(Exception):
            send_callback(callback_url, data)
        mock_post.assert_called_once_with(callback_url, json=data, timeout=10.0)
