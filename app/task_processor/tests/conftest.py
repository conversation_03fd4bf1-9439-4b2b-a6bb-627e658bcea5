import os
import sys
from typing import Callable

import boto3
import botocore.client
import pytest
from common.enums import BatchJobStatusEnum
from faker import Faker
from fastapi import Request
from fastapi.testclient import TestClient
from moto import mock_aws

faker = Faker()


@pytest.mark.usefixtures(name="mock_provider")
class MockProvider:
    """
    Provider for mocking requests.
    """

    def _ssm_setup(self):
        """
        Mocks param store setup.
        """
        region_name = os.environ["aws_region"]
        self.ssm_client = boto3.client("ssm", region_name=region_name)
        self.ssm_client.put_parameter(
            Name="/alpha/test/global/Web/Jwt/Key",
            Value=os.environ["jwt_key"],
            Type="SecureString",
        )
        self.ssm_client.put_parameter(
            Name="/alpha/test/global/Web/Jwt/Issuer",
            Value=os.environ["jwt_issuer"],
            Type="String",
        )
        self.ssm_client.put_parameter(
            Name="/alpha/test/global/Web/Jwt/Audience",
            Value=os.environ["jwt_audience"],
            Type="String",
        )
        assert isinstance(self.ssm_client, botocore.client.BaseClient)
        assert self.ssm_client.meta.service_model.service_name == "ssm"

    def set_up_mock(self):
        """
        Mocks param store setup.
        """
        self._ssm_setup()


@pytest.fixture
def fastapi_client(
    state: dict = {
        "client_guid": faker.uuid4(),
        "user_guid": faker.uuid4(),
    },
):
    from api.main import app

    class CustomTestClient(TestClient):
        def __init__(self, app):
            self._mystate = state

            # Reset middleware stack
            app.user_middleware = []
            app.middleware_stack = None

            @app.middleware("http")
            async def add_state(request: Request, call_next: Callable):
                for key, value in self.state.items():
                    setattr(request.state, key, value)
                response = await call_next(request)
                return response

            # Initialize the TestClient
            super().__init__(app, base_url=f"http://testserver/api/encore/v1")

        @property
        def state(self) -> dict:
            return self._mystate

    with CustomTestClient(app) as client:
        yield client


@pytest.fixture
def s3_setup():
    with mock_aws():
        client = boto3.client("s3", region_name="us-east-1")
        # Creating mocked s3 paths for testing
        bucket_name = "aca-dev-alpha-copilot-us-east-1"
        client.create_bucket(Bucket=bucket_name)
        client.put_object(Bucket=bucket_name, Key="test_data/test-docx.pdf", Body=b"Test PDF Content")
        client.put_object(Bucket=bucket_name, Key="test_data/test-pptx.pdf", Body=b"Test PPTX Content")
        yield client


@pytest.fixture
def fake_batch_job_id():
    """Fixture to generate a fake job ID."""
    return Faker().uuid4()


@pytest.fixture
def fake_batch_job_in_processing_state():
    """Fixture to generate a fake job in processing state."""
    return {
        "job_id": fake_batch_job_id,
        "status": BatchJobStatusEnum.PROCESSING.value,
        "client_guid": Faker().uuid4(),
        "user_guid": Faker().uuid4(),
        "job_params": {
            "location": "s3://bucket-name/path/to/file.json",
            "prompt": "You are a helpful assistant",
            "model": "azure:gpt-4o",
        },
        "results": None,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:00:00Z",
    }


@pytest.fixture
def fake_batch_job_in_completed_state():
    """Fixture to generate a fake job in completed state."""
    return {
        "job_id": fake_batch_job_id,
        "status": BatchJobStatusEnum.COMPLETED.value,
        "client_guid": Faker().uuid4(),
        "user_guid": Faker().uuid4(),
        "job_params": {
            "location": "s3://bucket-name/path/to/file.json",
            "prompt": "You are a helpful assistant",
            "model": "azure:gpt-4o",
        },
        "results": None,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:00:00Z",
    }
