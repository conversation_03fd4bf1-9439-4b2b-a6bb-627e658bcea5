import uuid
from unittest import mock

from common.enums import BatchJobStatusEnum
from task_processor.tasks import task_generate_batch


class TestTaskGenerateBatch:
    def setup_method(self):
        self.job_id = str(uuid.uuid4())
        self.job_id_obj = uuid.UUID(self.job_id)

        self.job_params = {
            "prompt": "Test prompt",
            "location": "https://example.com/data.json",
            "model": "test-model",
            "temperature": 0.7,
            "top_p": 0.9,
            "output_type": "text",
            "client_guid": "client123",
        }

        self.job_data = {
            "id": self.job_id,
            "job_params": self.job_params,
            "status": BatchJobStatusEnum.PENDING.value,
        }

        self.input_data = [
            {"context": "Context 1"},
            {"context": "Context 2"},
            {"context": "Context 3"},
        ]

        self.mock_get_batch_job_manager = mock.patch("task_processor.tasks.generate.get_batch_job_manager").start()

        self.mock_fetch_input_data = mock.patch("task_processor.tasks.generate.fetch_input_data").start()

        self.mock_get_s3_storage = mock.patch("task_processor.tasks.generate.get_s3_storage_for_job_results").start()

        self.mock_task_generate = mock.patch("task_processor.tasks.generate.task_generate").start()

        self.mock_batch_job = mock.MagicMock()
        self.mock_get_batch_job_manager.return_value = self.mock_batch_job
        self.mock_batch_job.get_job.return_value = self.job_data

        self.mock_fetch_input_data.return_value = self.input_data

        self.mock_s3_instance = mock.MagicMock()
        self.mock_get_s3_storage.return_value = self.mock_s3_instance
        self.mock_s3_instance.get_job_prefix.return_value = f"jobs/{self.job_id}/"

    def teardown_method(self):
        mock.patch.stopall()

    def test_successful_batch_processing(self):
        task_generate_batch(self.job_id)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)

        self.mock_fetch_input_data.assert_called_once_with(self.job_params["location"])

        update_calls = self.mock_batch_job.update_job.call_args_list
        assert len(update_calls) == 2

        # 1st update should set status to PROCESSING
        args, kwargs = update_calls[0]
        assert kwargs["job_id"] == self.job_id_obj
        assert kwargs["status"] == BatchJobStatusEnum.PROCESSING.value
        assert "started_at" in kwargs

        # 2nd update should set total_items
        args, kwargs = update_calls[1]
        assert kwargs["job_id"] == self.job_id_obj
        assert kwargs["status"] == BatchJobStatusEnum.FAILED.value
        assert "status_description" in kwargs

        assert self.mock_task_generate.send.call_count == 0

        for i, call in enumerate(self.mock_task_generate.send.call_args_list):
            args, kwargs = call
            request_data = args[0]
            assert request_data["id"] == f"{self.job_id}_{i}"
            assert request_data["prompt"] == "Test prompt"
            assert request_data["context"] == self.input_data[i]
            assert request_data["model"] == "test-model"
            assert request_data["job_id"] == self.job_id

    def test_job_not_found(self):
        self.mock_batch_job.get_job.return_value = None

        task_generate_batch(self.job_id)

        self.mock_batch_job.get_job.assert_called_once_with(job_id=self.job_id_obj)
        self.mock_fetch_input_data.assert_not_called()
        self.mock_batch_job.update_job.assert_not_called()
        self.mock_task_generate.send.assert_not_called()

    def test_no_input_data(self):
        self.mock_fetch_input_data.return_value = []

        task_generate_batch(self.job_id)

        self.mock_fetch_input_data.assert_called_once_with(self.job_params["location"])
        assert self.mock_batch_job.update_job.call_count == 2
        self.mock_task_generate.send.assert_not_called()

    def test_fetch_input_exception(self):
        self.mock_fetch_input_data.side_effect = Exception("Fetch error")

        task_generate_batch(self.job_id)

        self.mock_fetch_input_data.assert_called_once_with(self.job_params["location"])

        update_calls = self.mock_batch_job.update_job.call_args_list
        final_call = update_calls[-1]
        args, kwargs = final_call
        assert kwargs["job_id"] == self.job_id_obj
        assert kwargs["status"] == BatchJobStatusEnum.FAILED.value
        assert "status_description" in kwargs
        self.mock_task_generate.send.assert_not_called()
