FROM python:3.11-slim-bullseye AS base

ENV DEBIAN_FRONTEND=noninteractive

RUN groupadd -r appuser && \
    useradd -r -g appuser -d /home/<USER>
    apt-get update && \
    apt-get install -y curl procps && \
    apt-get clean && \
    apt-get install -y build-essential wget ca-certificates libpq-dev && \
    wget https://www.sqlite.org/2023/sqlite-autoconf-3410200.tar.gz && \
    tar -xvf sqlite-autoconf-3410200.tar.gz && \
    cd sqlite-autoconf-3410200 && \
    ./configure && \
    make && \
    make install && \
    cd .. && \
    rm -rf sqlite-autoconf-3410200.tar.gz sqlite-autoconf-3410200 && \
    apt-get purge -y --auto-remove wget build-essential && \
    apt-get install -y libpq5 && \
    rm -rf /var/lib/apt/lists/*

FROM base AS builder

COPY --from=ghcr.io/astral-sh/uv:0.5.18 /uv /bin/uv
ENV UV_COMPILE_BYTECODE=1 UV_LINK_MODE=copy

WORKDIR /app

ARG PACKAGE=task-processor

COPY pyproject.toml uv.lock /app/

COPY app/task_processor/pyproject.toml /app/app/task_processor/
COPY app/common/pyproject.toml /app/app/common/

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project --no-dev --package "${PACKAGE}"

COPY app/task_processor/src/ /app/
COPY app/task_processor/scripts/ /app/scripts
COPY app/common/src/common /app/common

FROM base
COPY --from=builder --chown=appuser:appuser /app /app

ENV PATH="/app/.venv/bin:$PATH"

ARG Environment
ENV env_name=${Environment}

WORKDIR /app

USER appuser

EXPOSE 8080

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1"]
