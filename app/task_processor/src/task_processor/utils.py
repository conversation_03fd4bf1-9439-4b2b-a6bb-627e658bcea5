"""Task Processor Utility Functions."""

from typing import Any

import httpx
from common.logging import LoggingSetup
from common.settings import settings

log = LoggingSetup.setup_logger("task_processor_utils")


def send_callback(callback_url: str, data: dict[str, Any]) -> None:
    """Send a callback with result data.

    Args:
        callback_url: URL to send callback to
        data: Data to include in callback
    """
    try:
        response = httpx.post(callback_url, json=data, timeout=settings.task_processor.callback_timeout)
        response.raise_for_status()
        log.info("[TASK]-[GENERATE] Callback sent successfully to %s", callback_url)
    except httpx.HTTPStatusError as e:
        log.error("[TASK]-[GENERATE] Error sending callback to %s: %s", callback_url, e, exc_info=True)
