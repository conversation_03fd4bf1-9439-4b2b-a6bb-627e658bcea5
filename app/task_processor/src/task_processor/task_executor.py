"""Task execution handlers."""

from __future__ import annotations

import time
import uuid
from typing import TYPE_CHECKING, Any

import boto3
import litellm.proxy.client
from botocore.exceptions import ClientError
from common.logging import LoggingSetup
from common.settings import settings

if TYPE_CHECKING:  # pragma: no cover
    from mypy_boto3_transcribe.client import TranscribeServiceClient

log = LoggingSetup.setup_logger("task_executor")


def handle_completion_task(task: dict[str, Any]) -> dict[str, Any]:
    """Execute a text completion task via LiteLLM."""
    api_key = task.get("api_key")
    client = litellm.proxy.client.Client(base_url=settings.llm_gateway.base_url, api_key=api_key)
    try:
        return client.http.request(  # type: ignore
            method="POST",
            uri="/completions",
            json=task,
            headers={"Content-Type": "application/json"},
        )
    except Exception as e:  # pylint: disable=broad-exception-caught
        log.error("Completion task failed: %s", e, exc_info=True)
        return {"status": "failed", "error": str(e)}


def handle_transcription_task(task: dict[str, Any]) -> dict[str, Any]:
    """Execute a transcription task using AWS Transcribe."""
    transcribe_client: TranscribeServiceClient = boto3.client("transcribe")
    job_name = f"encore-{uuid.uuid4()}"
    try:
        transcribe_client.start_transcription_job(
            TranscriptionJobName=job_name,
            Media={"MediaFileUri": task["file"]},
            MediaFormat=task.get("media_format") or "mp3",
            LanguageCode=task.get("language_code") or "en-US",
            OutputBucketName=settings.task_processor.bucket_name,
            OutputKey=f"transcriptions/{job_name}.json",
        )
    except ClientError as e:
        log.error("Failed to start transcription: %s", e, exc_info=True)
        return {"status": "failed", "error": str(e)}

    status = "IN_PROGRESS"
    while status in {"IN_PROGRESS", "QUEUED"}:
        time.sleep(5)
        try:
            resp = transcribe_client.get_transcription_job(TranscriptionJobName=job_name)
            status = resp["TranscriptionJob"]["TranscriptionJobStatus"]
        except ClientError as e:  # pragma: no cover
            log.error("Error polling transcription job: %s", e, exc_info=True)
            return {"status": "failed", "error": str(e)}

    if status == "COMPLETED":
        uri = resp["TranscriptionJob"]["Transcript"]["TranscriptFileUri"]
        return {"status": "completed", "transcript_uri": uri}

    return {
        "status": "failed",
        "error": resp["TranscriptionJob"].get("FailureReason", "Unknown"),
    }
