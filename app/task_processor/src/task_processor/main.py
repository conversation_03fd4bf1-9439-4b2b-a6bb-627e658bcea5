"""Main entry point for the task processor service."""

import sys

from common.logging import LoggingSetup

from task_processor.sqs_processor import SQSProcessor

log = LoggingSetup.setup_logger("main")


if __name__ == "__main__":
    try:
        log.info("Starting task processor")
        SQSProcessor.start()
    except KeyboardInterrupt:
        log.info("Task processor interrupted by user")
    except Exception as e:  # pylint: disable=broad-exception-caught
        log.error("Unhandled exception: %s", e, exc_info=True)
        sys.exit(1)
