"""Module for processing messages from SQS queues."""

import json
import os
import signal
import threading
import time
import uuid
from typing import TYPE_CHECKING, Any

import boto3
from botocore.exceptions import ClientError
from common.logging import LoggingSetup
from common.services.job_store import BatchJobStatusEnum, JobStore
from common.settings import settings
from common.utils import get_current_time

from task_processor import task_executor

if TYPE_CHECKING:
    from mypy_boto3_sqs import SQSClient


log = LoggingSetup.setup_logger("sqs_processor")


class GracefulShutdown:
    """Helper class to handle graceful shutdown."""

    def __init__(self):
        self.shutdown_event = threading.Event()
        self.original_sigint = signal.getsignal(signal.SIGINT)
        self.original_sigterm = signal.getsignal(signal.SIGTERM)
        signal.signal(signal.SIGINT, self._handle_signal)
        signal.signal(signal.SIGTERM, self._handle_signal)

    def _handle_signal(self, signum, frame):
        log.info("Received signal %s, initiating graceful shutdown", signum)
        self.shutdown_event.set()

    def __enter__(self):
        return self.shutdown_event

    def __exit__(self, exc_type, exc_val, exc_tb):
        signal.signal(signal.SIGINT, self.original_sigint)
        signal.signal(signal.SIGTERM, self.original_sigterm)


class SQSProcessor:
    """Processor for SQS messages."""

    sqs_client: "SQSClient"
    job_store: JobStore
    queue_url: str

    def __init__(self):
        """Initialize the SQS processor.

        Args:
            queue_url: URL of the SQS queue to listen to
            batch_job: BatchJob instance for database operations
        """
        self.queue_name = (
            f"sqs-{os.environ.get('env_name')}-{settings.task_processor.task_queue_namespace}-{os.environ.get('AWS_REGION')}.fifo"
        )
        self.job_store = JobStore.get_instance()
        self.sqs_client = boto3.client("sqs")

        self.queue_url = self._get_queue_url()

    def _get_queue_url(self) -> str:
        """Get the SQS queue URL based on the configured queue name."""
        # list existing queues
        try:
            response = self.sqs_client.get_queue_url(QueueName=self.queue_name)
            return response["QueueUrl"]
        except ClientError as e:
            log.error("Error getting SQS queue URL: %s", e, exc_info=True)
            raise RuntimeError(f"Failed to get queue URL for {self.queue_name}") from e

    @classmethod
    def start(cls):
        # Initialize the SQS processor
        processor = SQSProcessor()

        with GracefulShutdown() as shutdown_event:
            try:
                processor.start_processing(shutdown_event=shutdown_event)
            except Exception as e:  # pylint: disable=broad-exception-caught
                log.error("Error in processor: %s", e, exc_info=True)

            log.info("Task processor stopped")

    def start_processing(self, shutdown_event: threading.Event):
        """Start processing messages from the queue.

        Args:
            polling_interval: Seconds to wait between polling for messages
            shutdown_event: Event to signal shutdown (for graceful termination)
        """
        log.info("Starting SQS message processor for queue: %s", self.queue_url)

        while not shutdown_event.is_set():
            try:
                # Receive a batch of messages from the queue using long polling (WaitTimeSeconds) for efficiency
                response = self.sqs_client.receive_message(
                    QueueUrl=self.queue_url,
                    MaxNumberOfMessages=settings.task_processor.batch_size,  # Get up to 10 messages at once
                    VisibilityTimeout=settings.task_processor.task_visibility_timeout,  # How long should we hide these from other consumers
                    WaitTimeSeconds=settings.task_processor.polling_interval_seconds,  # Long polling
                    MessageAttributeNames=["All"],  # Get all message attributes
                )

                if "Messages" not in response:
                    log.debug("No messages received")  # TODO
                    continue

                messages = response["Messages"]
                log.info("Received %s messages", len(messages))

                # Group messages by MessageGroupId (client)
                message_groups: dict[str, list[dict[str, Any]]] = {}
                for message in messages:
                    body = json.loads(message["Body"])
                    message_id = message["MessageId"]
                    receipt_handle = message["ReceiptHandle"]

                    # Extract message attributes
                    attrs = message.get("MessageAttributes", {})
                    job_id = attrs.get("JobId", {}).get("StringValue")
                    message_group_id = attrs.get("MessageGroupId", {}).get("StringValue", "")
                    message_type = attrs.get("MessageType", {}).get("StringValue")
                    batch_seq = int(attrs.get("BatchSequence", {}).get("StringValue", "0"))
                    batch_total = int(attrs.get("BatchTotal", {}).get("StringValue", "0"))
                    timestamp = attrs.get("Timestamp", {}).get("StringValue")

                    if not message_type or not job_id:
                        log.warning("Message %s missing required attributes, skipping", message_id)
                        self._delete_message(receipt_handle)
                        continue

                    # Add message to its group
                    if message_group_id not in message_groups:
                        message_groups[message_group_id] = []

                    message_groups[message_group_id].append({
                        "body": body,
                        "message_id": message_id,
                        "receipt_handle": receipt_handle,
                        "job_id": job_id,
                        "message_type": message_type,
                        "batch_seq": batch_seq,
                        "batch_total": batch_total,
                        "timestamp": timestamp,
                    })

                # Process each client's tasks separately
                for group_id, group_messages in message_groups.items():
                    self._process_message_group(group_messages)

            except ClientError as e:
                log.error("AWS SQS error: %s", e, exc_info=True)
                time.sleep(settings.task_processor.polling_interval_seconds)
            except Exception as e:  # pylint: disable=broad-exception-caught
                log.error("Unexpected error: %s", e, exc_info=True)
                time.sleep(settings.task_processor.polling_interval_seconds)

    def _process_message_group(self, messages: list[dict[str, Any]]):
        """Process a group of messages for the same client.

        Args:
            group_id: The message group ID (client GUID)
            messages: List of message data dictionaries
        """
        if not messages:
            return

        # Group messages by job_id
        job_messages: dict[str, list[dict[str, Any]]] = {}
        for message in messages:
            job_id = message["job_id"]
            if job_id not in job_messages:
                job_messages[job_id] = []

            job_messages[job_id].append(message)

        # Process each job's messages separately
        for job_id, job_batch in job_messages.items():
            self._process_job_batch(job_id, job_batch)

    def _process_job_batch(self, job_id: str, messages: list[dict[str, Any]]):
        """Process a batch of messages for a specific job.

        Args:
            job_id: The job ID
            messages: List of message data dictionaries for this job
        """
        if not messages:
            return

        job_uuid = uuid.UUID(job_id)
        job = self.job_store.get_job(job_id=job_uuid)

        # For each message in this batch
        batch_seq = 0
        job.results = job.results or []

        for message in messages:
            batch_seq = message["batch_seq"]
            body = message["body"]
            message_type = message["message_type"]

            # Skip if we've already processed this sequence number
            if batch_seq <= job.processed_count:
                self._delete_message(message["receipt_handle"])
                continue

            result = self.execute_task(message_type, body)

            job.results.append(result)
            self._delete_message(message["receipt_handle"])

        job.processed_count += len(job.results)

        if batch_seq == job.total_count:
            if job.processed_count < job.total_count:
                job.status = BatchJobStatusEnum.PARTIALLY_COMPLETED
            else:
                job.status = BatchJobStatusEnum.COMPLETED
            job.completed_at = get_current_time()
        else:
            job.status = BatchJobStatusEnum.PROCESSING

        job.updated_at = get_current_time()

        try:
            self.job_store.update_job(job)

            log.info("Updated job %s status to PROCESSING", job_id)
        except Exception as e:  # pylint: disable=broad-exception-caught
            log.error("Error updating job %s status: %s", job_id, e, exc_info=True)

    def execute_task(self, message_type: str, task: dict[str, Any]) -> dict[str, Any]:
        """Execute a task based on its type.
        Args:
            type: The type of task to execute
            task: The task data
        Returns:
            dict: Result of the task execution
        """
        log.info("Executing task of type %s", message_type)

        # Map of task types to handler functions
        task_handlers = {
            "CompletionRequest": task_executor.handle_completion_task,
            "TranscriptionRequest": task_executor.handle_transcription_task,
            # Add more handlers as needed...
        }

        # Get the appropriate handler for this task type
        handler = task_handlers.get(message_type)

        if handler:
            try:
                return handler(task)
            except Exception as e:  # pylint: disable=broad-exception-caught
                log.error("Error executing task of type %s: %s", message_type, e, exc_info=True)
                return {"error": str(e), "status": "failed"}
        else:
            log.warning("Unknown task type: %s", message_type)
            return {"error": f"Unknown task type: {message_type}", "status": "failed"}

    def _delete_message(self, receipt_handle: str):
        """Delete a message from the queue.

        Args:
            receipt_handle: The receipt handle of the message
        """
        try:
            self.sqs_client.delete_message(QueueUrl=self.queue_url, ReceiptHandle=receipt_handle)
        except ClientError as e:
            log.error("Error deleting message: %s", e, exc_info=True)  # TODO what do we do if this happens?
