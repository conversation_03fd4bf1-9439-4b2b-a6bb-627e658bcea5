servers:
  # - namespace: stripe
  #   name: Stripe API
  #   url: https://raw.githubusercontent.com/stripe/openapi/refs/heads/master/openapi/spec3.yaml
  #   base_url: https://api.stripe.com
  #   # Select which API paths to expose over MCP. Each matching path will become a tool with arguments
  #   # from the query parameters or the JSON body.
  #   # For example, we're only exposing the endpoints to GET/POST a customer.
  #   paths:
  #     - /v1/customers$
  #   # Forward the Authorization header to the Stripe API
  #   forward_headers:
  #     - Authorization

  # - namespace: zendesk
  #   # This is the name as it appears to the LLM
  #   name: Zendesk API
  #   url: https://developer.zendesk.com/zendesk/oas.yaml
  #   # Change the subdomain to match your Zendesk instance
  #   base_url: https://{subdomain}.zendesk.com
  #   # Select which API paths to expose over MCP
  #   paths:
  #     - /api/v2/tickets$
  #   # Forward the Authorization header to the Zendesk API
  #   forward_headers:
  #     - Authorization

  # - namespace: weather
  #   name: Open Weather API
  #   url: https://gist.githubusercontent.com/mor10/6fb15f2270f8ac1d8b99aa66f9b63410/raw/0e2c4ed43eb4c126ec2284bc7c069de488b53d99/openweatherAPI.json
  #   base_url: https://api.openweathermap.org
  #   paths:
  #     - /data/2.5/weather
  #   # Forward the x-open-weather-app-id header to the api_id query parameter.
  #   forward_query_params:
  #     x-open-weather-app-id: appid
  #   timeout: 0.5

  # Just an example to show reading a local file url
  # - namespace: httpbin
  #   name: httpbin
  #   url: file://test-specs/httpbin.yaml
  #   base_url: https://httpbin.org
  #   paths:
  #     - /get

  - namespace: EC
    name: Employee Compliance API
    url: https://app.compliancealpha.com/api/fd/swagger/public/swagger.json
    base_url: https://app.compliancealpha.com
    paths:
      - /v1/transactions$
