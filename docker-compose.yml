version: "3.8"

services:
  api:
    build:
      context: ./app
      dockerfile: ./api/Dockerfile
      args:
        Environment: ${ENVIRONMENT:-dev}
        PACKAGE: api
    command: /app/scripts/run_api.sh
    ports:
      - "8080:8080"
    volumes:
      - ./app/api/src:/app/api
      - ./app/common/src:/app/common
      - ./app/task_processor/src:/app/task_processor
      - ./scripts:/app/scripts
    environment:
      - env_name=${ENVIRONMENT:-dev}
    depends_on:
      - llm_gateway
    # Add healthcheck to ensure the service is running properly
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/api/encore/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
  task_processor:
    build:
      context: ./app
      dockerfile: ./task_processor/Dockerfile
      args:
        Environment: ${ENVIRONMENT:-dev}
        PACKAGE: task-processor
    command: /app/scripts/run_task_processor.sh
    volumes:
      - ./app/api/src:/app/api
      - ./app/common/src:/app/common
      - ./app/task_processor/src:/app/task_processor
      - ./scripts:/app/scripts
    environment:
      - env_name=${ENVIRONMENT:-dev}
    depends_on:
      - llm_gateway
    healthcheck:
      test: [ "CMD", "pgrep", "-f", "task_processor" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
  llm_gateway:
    build:
      context: ./app/llm_gateway
      dockerfile: ./Dockerfile
      args:
        Environment: ${ENVIRONMENT:-dev}
    ports:
      - "8008:8008"
    environment:
      - env_name=${ENVIRONMENT:-dev}
    healthcheck:
      test: [ "CMD", "python", "-c", "import requests; exit() if requests.get('http://localhost:8008/health/readiness').status_code == 200 else exit(1)" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
