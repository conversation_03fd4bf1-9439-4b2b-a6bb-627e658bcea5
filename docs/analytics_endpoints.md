# Analytics Router Endpoints Documentation

## Overview

The Analytics Router provides comprehensive endpoints for tracking and analyzing LiteLLM tag usage and spend metrics. All endpoints are prefixed with `/v1/analytics` and provide detailed insights into API usage patterns, costs, and performance metrics.

## Base URL

```
/v1/analytics
```

## Authentication

All endpoints require API key authentication via the `Authorization` header:

```
Authorization: Bearer <your-api-key>
```

## Endpoints

### 1. Get Tag Usage Summary

**Endpoint:** `GET /v1/analytics/summary`

**Description:** Get a summary of usage for a specific tag including total spend, requests, and tokens.

**Query Parameters:**

- `tag` (required): Tag to analyze
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

**Response Model:**

```json
{
	"tag": "string",
	"total_spend": 0.0,
	"total_requests": 0,
	"total_tokens": 0,
	"success_rate": 0.0,
	"date_range": "string"
}
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/summary?tag=production&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
{
	"tag": "production",
	"total_spend": 125.5,
	"total_requests": 1500,
	"total_tokens": 45000,
	"success_rate": 98.5,
	"date_range": "2024-01-01 to 2024-01-31"
}
```

**Error Responses:**

- `404 Not Found`: No data found for the specified tag
- `500 Internal Server Error`: Database or server error

---

### 2. Get Detailed Tag Usage Records

**Endpoint:** `GET /v1/analytics/details`

**Description:** Get detailed usage records for a specific tag with pagination support.

**Query Parameters:**

- `tag` (required): Tag to analyze
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format
- `limit` (optional): Maximum number of records to return (default: 100, max: 1000)
- `offset` (optional): Number of records to skip (default: 0)

**Response Model:**

```json
[
	{
		"id": "uuid",
		"tag": "string",
		"date": "string",
		"api_key": "string",
		"model": "string",
		"model_group": "string",
		"custom_llm_provider": "string",
		"prompt_tokens": 0,
		"completion_tokens": 0,
		"cache_read_input_tokens": 0,
		"cache_creation_input_tokens": 0,
		"spend": 0.0,
		"api_requests": 0,
		"successful_requests": 0,
		"failed_requests": 0,
		"created_at": "datetime",
		"updated_at": "datetime"
	}
]
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/details?tag=production&limit=10&offset=0" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
[
	{
		"id": "123e4567-e89b-12d3-a456-************",
		"tag": "production",
		"date": "2024-01-15",
		"api_key": "sk-1234567890abcdef",
		"model": "gpt-4",
		"model_group": "openai",
		"custom_llm_provider": "azure",
		"prompt_tokens": 150,
		"completion_tokens": 75,
		"cache_read_input_tokens": 0,
		"cache_creation_input_tokens": 0,
		"spend": 0.045,
		"api_requests": 1,
		"successful_requests": 1,
		"failed_requests": 0,
		"created_at": "2024-01-15T10:30:00Z",
		"updated_at": "2024-01-15T10:30:00Z"
	}
]
```

---

### 3. Get Daily Tag Usage Breakdown

**Endpoint:** `GET /v1/analytics/daily`

**Description:** Get daily aggregated usage data for a specific tag.

**Query Parameters:**

- `tag` (required): Tag to analyze
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

**Response Model:**

```json
[
	{
		"date": "string",
		"spend": 0.0,
		"api_requests": 0,
		"total_tokens": 0,
		"success_rate": 0.0
	}
]
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/daily?tag=production&start_date=2024-01-01&end_date=2024-01-07" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
[
	{
		"date": "2024-01-07",
		"spend": 15.75,
		"api_requests": 45,
		"total_tokens": 1350,
		"success_rate": 97.8
	},
	{
		"date": "2024-01-06",
		"spend": 12.3,
		"api_requests": 38,
		"total_tokens": 1140,
		"success_rate": 100.0
	}
]
```

---

### 4. Get Tag Usage by Model

**Endpoint:** `GET /v1/analytics/models`

**Description:** Get usage breakdown by model for a specific tag.

**Query Parameters:**

- `tag` (required): Tag to analyze
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

**Response Model:**

```json
[
	{
		"model": "string",
		"model_group": "string",
		"custom_llm_provider": "string",
		"total_spend": 0.0,
		"total_requests": 0,
		"total_tokens": 0,
		"success_rate": 0.0
	}
]
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/models?tag=production" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
[
	{
		"model": "gpt-4",
		"model_group": "openai",
		"custom_llm_provider": "azure",
		"total_spend": 85.25,
		"total_requests": 850,
		"total_tokens": 25500,
		"success_rate": 98.2
	},
	{
		"model": "gpt-3.5-turbo",
		"model_group": "openai",
		"custom_llm_provider": "azure",
		"total_spend": 40.25,
		"total_requests": 650,
		"total_tokens": 19500,
		"success_rate": 99.1
	}
]
```

---

### 5. Get Comprehensive Tag Analytics

**Endpoint:** `GET /v1/analytics/comprehensive`

**Description:** Get comprehensive analytics for a specific tag including summary, daily breakdown, and model analysis.

**Query Parameters:**

- `tag` (required): Tag to analyze
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

**Response Model:**

```json
{
	"tag": "string",
	"summary": {
		"tag": "string",
		"total_spend": 0.0,
		"total_requests": 0,
		"total_tokens": 0,
		"success_rate": 0.0,
		"date_range": "string"
	},
	"daily_data": [
		{
			"date": "string",
			"spend": 0.0,
			"api_requests": 0,
			"total_tokens": 0,
			"success_rate": 0.0
		}
	],
	"model_breakdown": [
		{
			"model": "string",
			"model_group": "string",
			"custom_llm_provider": "string",
			"total_spend": 0.0,
			"total_requests": 0,
			"total_tokens": 0,
			"success_rate": 0.0
		}
	],
	"total_records": 0
}
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/comprehensive?tag=production&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
{
	"tag": "production",
	"summary": {
		"tag": "production",
		"total_spend": 125.5,
		"total_requests": 1500,
		"total_tokens": 45000,
		"success_rate": 98.5,
		"date_range": "2024-01-01 to 2024-01-31"
	},
	"daily_data": [
		{
			"date": "2024-01-31",
			"spend": 4.25,
			"api_requests": 12,
			"total_tokens": 360,
			"success_rate": 100.0
		}
	],
	"model_breakdown": [
		{
			"model": "gpt-4",
			"model_group": "openai",
			"custom_llm_provider": "azure",
			"total_spend": 85.25,
			"total_requests": 850,
			"total_tokens": 25500,
			"success_rate": 98.2
		}
	],
	"total_records": 1500
}
```

---

### 6. List All Available Tags

**Endpoint:** `GET /v1/analytics/tags`

**Description:** Get a list of all available tags in the system.

**Query Parameters:** None

**Response Model:**

```json
["string"]
```

**Example Request:**

```bash
curl -X GET "http://localhost:8000/v1/analytics/tags" \
  -H "Authorization: Bearer your-api-key"
```

**Example Response:**

```json
["production", "development", "testing", "staging", "demo"]
```

---

## Error Handling

All endpoints follow consistent error handling patterns:

### HTTP Status Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Missing or invalid API key
- `404 Not Found`: No data found for the specified tag
- `422 Unprocessable Entity`: Validation error in request parameters
- `500 Internal Server Error`: Server or database error

### Error Response Format

```json
{
	"detail": "Error message description"
}
```

### Common Error Scenarios

1. **Invalid Date Format**: Dates must be in YYYY-MM-DD format
2. **Tag Not Found**: Returns 404 when no data exists for the specified tag
3. **Invalid Pagination**: Limit must be between 1-1000, offset must be >= 0
4. **Database Errors**: Returns 500 with error details

## Usage Examples

### Basic Usage

```bash
# Get summary for a specific tag
curl -X GET "http://localhost:8000/v1/analytics/summary?tag=production" \
  -H "Authorization: Bearer your-api-key"

# Get daily breakdown for last 7 days
curl -X GET "http://localhost:8000/v1/analytics/daily?tag=production&start_date=2024-01-01&end_date=2024-01-07" \
  -H "Authorization: Bearer your-api-key"
```

### Advanced Usage

```bash
# Get comprehensive analytics with date filtering
curl -X GET "http://localhost:8000/v1/analytics/comprehensive?tag=production&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer your-api-key"

# Get detailed records with pagination
curl -X GET "http://localhost:8000/v1/analytics/details?tag=production&limit=50&offset=100" \
  -H "Authorization: Bearer your-api-key"
```

### Python Client Example

```python
import requests

base_url = "http://localhost:8000/v1/analytics"
headers = {"Authorization": "Bearer your-api-key"}

# Get tag summary
response = requests.get(f"{base_url}/summary?tag=production", headers=headers)
summary = response.json()

# Get daily usage
response = requests.get(f"{base_url}/daily?tag=production", headers=headers)
daily_data = response.json()

# Get comprehensive analytics
response = requests.get(f"{base_url}/comprehensive?tag=production", headers=headers)
analytics = response.json()
```

## Data Models

### TagUsageSummary

- `tag`: The tag identifier
- `total_spend`: Total cost in USD
- `total_requests`: Total number of API requests
- `total_tokens`: Total tokens used (prompt + completion)
- `success_rate`: Percentage of successful requests
- `date_range`: Date range of the data

### TagUsageDetail

- `id`: Unique record identifier
- `tag`: The tag identifier
- `date`: Date of the usage
- `api_key`: API key used (may be masked)
- `model`: Model name used
- `model_group`: Model group (e.g., "openai", "anthropic")
- `custom_llm_provider`: Custom provider if applicable
- `prompt_tokens`: Number of prompt tokens
- `completion_tokens`: Number of completion tokens
- `cache_read_input_tokens`: Cache read tokens
- `cache_creation_input_tokens`: Cache creation tokens
- `spend`: Cost for this record
- `api_requests`: Number of API requests
- `successful_requests`: Number of successful requests
- `failed_requests`: Number of failed requests
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

### TagUsageByDate

- `date`: Date of the usage
- `spend`: Total spend for the date
- `api_requests`: Total API requests for the date
- `total_tokens`: Total tokens for the date
- `success_rate`: Success rate for the date

### TagUsageByModel

- `model`: Model name
- `model_group`: Model group
- `custom_llm_provider`: Custom provider
- `total_spend`: Total spend for this model
- `total_requests`: Total requests for this model
- `total_tokens`: Total tokens for this model
- `success_rate`: Success rate for this model

## Notes

- All monetary values are returned in USD
- Token counts include both prompt and completion tokens
- Success rates are calculated as (successful_requests / total_requests) \* 100
- Date filters are inclusive of the specified dates
- Results are ordered by date (newest first) for time-series data
- Model breakdown is ordered by total spend (highest first)
- The system automatically handles null values and provides appropriate defaults
