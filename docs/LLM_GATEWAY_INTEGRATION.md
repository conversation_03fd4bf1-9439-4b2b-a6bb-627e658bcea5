# LLM Gateway Integration

This document describes the integration of the LLM Gateway API with the Encore application.

## Overview

The LLM Gateway serves as a centralized proxy for all LLM (Large Language Model) requests in the Encore application. It runs as a separate container using LiteLLM and provides a unified API interface for multiple LLM providers (OpenAI, Azure OpenAI, Anthropic, AWS Bedrock, etc.).

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Encore API    │───▶│   LLM Gateway   │───▶│  LLM Providers  │
│                 │    │   (LiteLLM)     │    │ (OpenAI, Azure, │
│                 │    │                 │    │  Anthropic, etc)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
┌─────────────────┐           │
│ Task Processor  │───────────┘
└─────────────────┘
```

## Components

### 1. LLM Service Refactoring

The `LlmService` class has been refactored to:
- Use HTTP requests to communicate with the LLM Gateway instead of direct LLM provider APIs
- Support streaming responses
- Handle authentication via API keys
- Provide error handling and fallbacks

### 2. Infrastructure (CDK)

The CDK infrastructure includes:
- **Internal ALB**: LLM Gateway is only accessible via internal Application Load Balancer
- **ECS Service**: Runs the LiteLLM container with proper scaling configuration
- **Security**: No external internet access, only internal service-to-service communication

### 3. Configuration

#### Environment Variables
- `LLM_GATEWAY_BASE_URL`: URL of the LLM Gateway service
- `LLM_GATEWAY_API_KEY`: API key for authenticating with the gateway

#### AWS Parameter Store
- `/alpha/{env}/encore/llm/GatewayBaseUrl`: Internal ALB URL
- `/alpha/{env}/encore/llm/GatewayApiKey`: LiteLLM master key

## Local Development

### Starting Services

```bash
# Start all services including LLM Gateway
docker-compose up -d

# Check service health
curl http://localhost:8008/health/liveness
curl http://localhost:8080/api/encore/
```

### Testing LLM Gateway

```bash
# Run the integration test script
python scripts/test_llm_gateway.py

# Test via API
curl -X POST http://localhost:8080/api/encore/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Hello, how are you?",
    "context": "You are a helpful assistant"
  }'
```

## Production Deployment

### Prerequisites

1. **LiteLLM Master Key**: Set in deployment variables as `#{LiteLLMMasterKey}`
2. **LLM Provider Keys**: Ensure all provider API keys are properly configured
3. **Internal ALB**: The gateway will be accessible only via internal ALB

### Deployment Steps

1. Deploy infrastructure:
   ```bash
   cd infrastructure
   npm run deploy
   ```

2. Deploy LLM Gateway:
   ```bash
   cd llm_gateway
   ./deploy.regional.sh
   ```

3. Deploy API and Task Processor:
   ```bash
   cd api
   ./deploy.regional.sh
   ```

## Configuration Files

### LiteLLM Config (`llm_gateway/config.yaml`)

The configuration includes:
- Multiple model providers (OpenAI, Azure, Anthropic, AWS Bedrock)
- Authentication and security settings
- Caching and performance optimizations
- Health check endpoints

### CDK Infrastructure

- **CPU**: 1024 (1 vCPU)
- **Memory**: 2048 MB (2 GB)
- **Scaling**: 1-3 instances based on CPU/Memory utilization
- **Health Check**: `/health/liveness` endpoint
- **Internal ALB**: Priority 89, paths `/llm-gateway/*`, `/v1/*`, `/health/*`

## API Endpoints

The LLM Gateway exposes standard OpenAI-compatible endpoints:

- `POST /v1/chat/completions`: Chat completions
- `POST /v1/completions`: Text completions
- `POST /v1/embeddings`: Text embeddings
- `GET /health/liveness`: Health check

## Error Handling

The service includes comprehensive error handling:
- **Connection Errors**: Automatic retries with exponential backoff
- **Authentication Errors**: Proper error messages and logging
- **Rate Limiting**: Graceful handling of provider rate limits
- **Fallbacks**: Default responses when all providers fail

## Monitoring

Key metrics to monitor:
- **Response Time**: Average latency for LLM requests
- **Error Rate**: Failed requests percentage
- **Provider Usage**: Distribution across different LLM providers
- **Token Usage**: Consumption tracking for cost management

## Security

- **Internal Only**: Gateway is not exposed to the internet
- **API Key Authentication**: All requests require valid API key
- **Provider Keys**: Stored securely in AWS Parameter Store
- **Network Isolation**: Traffic flows only through internal ALB

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if LLM Gateway service is running
   - Verify internal ALB health checks pass
   - Ensure correct URL configuration

2. **Authentication Errors**
   - Verify LiteLLM master key is correctly set
   - Check API key configuration in Parameter Store

3. **Model Not Found**
   - Verify model name in LiteLLM config
   - Check provider API key validity
   - Ensure model is available in the region

### Logs

Check logs for debugging:
```bash
# Local development
docker-compose logs llm_gateway

# Production (AWS)
aws logs tail /aws/ecs/encore-llm-gateway-{env} --follow
```

## Migration Notes

### Breaking Changes

1. **LlmService API**: The internal implementation changed, but public API remains the same
2. **Dependencies**: Added `aiohttp` dependency for HTTP client functionality
3. **Configuration**: New environment variables for gateway connection

### Rollback Plan

If issues occur, you can temporarily rollback by:
1. Reverting `LlmService` to use direct provider APIs
2. Removing LLM Gateway from infrastructure
3. Updating configuration to use provider APIs directly

## Future Enhancements

- **Caching**: Implement Redis-based response caching
- **Load Balancing**: Smart routing between providers based on performance
- **Cost Optimization**: Automatic provider selection based on cost
- **Usage Analytics**: Detailed usage tracking and reporting
