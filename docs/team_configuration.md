# Team Configuration in AWS Secrets Manager

This document explains how to configure teams for LiteLLM initialization using AWS Secrets Manager.

## Overview

The initialization script expects each team configuration to be stored as a separate secret in AWS Secrets Manager. This approach provides better security and flexibility compared to storing all configurations in a single secret.

## Secret Naming Convention

Team configuration secrets should follow this naming pattern:

```
{prefix}/{team_name}
```

Where:

- `prefix` is defined by the `LITELLM_TEAM_SECRET_PREFIX` environment variable (default: `litellm/team/config/`)
- `team_name` is a unique identifier for the team

### Examples:

- `litellm/team/config/ecomms`
- `litellm/team/config/encore`
- `litellm/team/config/mr`

## Configuration Format

Each secret should contain a JSON object with the following structure:

```json
{
	"team_alias": "encore",
	"models": ["gpt-3.5-turbo", "gpt-4"],
	"max_budget": 1000.0,
	"duration": "30d"
}
```

### Required Fields

- `team_alias` (string): The unique identifier for the team. This will be used as the team alias in LiteLLM.

### Optional Fields

- `models` (array of strings): List of model names that this team can access. Default: `["gpt-3.5-turbo"]`
- `max_budget` (number): Maximum budget for the team in USD. Default: no limit
- `duration` (string): Duration for the API key (e.g., "30d", "1y"). Default: no expiration

## Environment Variables

The following environment variables can be configured:

- `LITELLM_TEAM_SECRET_PREFIX`: Prefix for team configuration secrets (default: `litellm/team/config/`)
- `AWS_REGION`: AWS region for Secrets Manager (default: `us-east-1`)

## Example Team Configurations

### Encore Team

```json
{
	"team_alias": "encore",
	"models": ["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"],
	"max_budget": 5000.0,
	"duration": "90d"
}
```

### Ecomms Team

```json
{
	"team_alias": "ecomms",
	"models": ["gpt-3.5-turbo", "gpt-4"],
	"max_budget": 2000.0,
	"duration": "60d"
}
```

### MR Team

```json
{
	"team_alias": "mr",
	"models": ["gpt-3.5-turbo"],
	"max_budget": 1000.0,
	"duration": "30d"
}
```

## Initialization Process

1. The initialization script lists all secrets with the configured prefix
2. For each secret, it retrieves the team configuration
3. Validates that the configuration contains required fields
4. Checks if the team already exists in LiteLLM
5. Creates the team if it doesn't exist
6. Generates an API key for the team
7. Stores the API key in a separate secret with the pattern `litellm/team/{team_id}/api_key`

## Error Handling

The initialization script handles various error scenarios:

- Missing or invalid JSON in configuration secrets
- Network errors when communicating with LiteLLM
- AWS Secrets Manager access issues
- Teams that already exist (skipped gracefully)

If initialization fails for a specific team, the script logs the error and continues with other teams. The FastAPI server will still start even if team initialization fails.
