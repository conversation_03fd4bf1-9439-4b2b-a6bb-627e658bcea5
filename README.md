# Encore(aca-ai)

This repository contains everything needed to build and deploy the backend for the Encore(ACA AI) Platform.

This is a monorepo that includes multiple components that work together to provide the functionality of the platform. The components are designed to be modular and can be deployed independently or together as needed.

## Components
Components are included in this repository:

- encore
  - api/ - the API we will expose externally (which will post on the SQS or call encore-llm-gateway API)
  - common/ - common code used by the API and task processor
  - scripts/ - scripts to help with development and deployment
  - task_processor/ - the background task processor that processes requests from the API and interacts with the llm_gateway
  - llm_gateway (image we build with litellm as base which adds any custom config, etc.) - thin wrapper around litellm
  - mcp_proxy/ - Model Context Protocol proxy (mcp_openapi)

## Local Development

To run the project locally, you will need to have the following installed:
- Docker
- Docker Compose

### Running the Project

To run the project locally, you can use Docker Compose. This will start all the necessary services defined in the `docker-compose.local.yml` file.

```bash
# if you are on Mac or Linux, you can run the following command to start the project:
./scripts/run

# if you are on Windows, you can run the following command to start the project:
docker compose down --remove-orphans
docker compose -f ./docker-compose.local.yml up api task_processor llm_gateway --build
```

### Sending the test request
To send a test request to the API, you can use the following command:

#### Health Check Example

```bash
curl -X GET http://localhost:8080/health/liveness
# or
curl -X GET http://localhost:8080/health/readiness
```

#### Text Generation Example

```python
import requests
token="Bearer sk-test"

headers = {
    "Authorization": token
}

url = "http://localhost:8080/api/encore/v1/chat/completions"

body = {
    "messages": [
        {
          "role": "system",
          "content": "You are a helpful assistant."
        },
        {
          "role": "user",
          "content": "What's the capital of France?"
        }
  ],
    "model": "openai:gpt-4o-mini",
}
r = requests.post(url, headers=headers, json=body)
print(r.json())
```

#### Embedding Example

```python
import requests
token="Bearer sk-test"

headers = {
    "Authorization": token
}
url = "http://localhost:8080/api/encore/v1/embeddings"
body = {
    "input": "Hello, world!",
    "model": "openai:text-embedding-3-small",
}
r = requests.post(url, headers=headers, json=body)
print(r.json())
```


### Running the linter
To run the linter, you can use the following command:

```bash
./scripts/shell

# and inside the container run:
./scripts/lint
```

