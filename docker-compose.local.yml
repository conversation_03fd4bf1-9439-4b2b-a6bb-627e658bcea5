services:
  api:
    build:
      context: .
      dockerfile: ./app/api/Dockerfile.local
      args:
        Environment: ${ENVIRONMENT:-dev}
        PACKAGE: api
    depends_on:
      - postgres_with_pgvector
      - task_processor
      - llm_gateway
      - localstack
    command:
      - '/bin/sh'
      - '-c'
      - '/bin/sleep 30 && /app/scripts/run_api.sh'
    ports:
      - "8080:8080"
    volumes:
      - ~/.aws/:/home/<USER>/.aws:ro
    networks:
      - my_network
    env_file:
      - ./app/api/.env
    environment:
      - env_name=${ENVIRONMENT:-dev}
      - AWS_ENDPOINT_URL=http://localstack:4566
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
    image: local_encore:latest
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/health/liveness" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
  postgres_with_pgvector:
    image: pgvector/pgvector:pg16
    container_name: postgresdb
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=encore
    networks:
      - my_network
    volumes:
      - postgres-db:/var/lib/postgresql/data
  task_processor:
    build:
      context: .
      dockerfile: ./app/task_processor/Dockerfile.local
      args:
        Environment: ${ENVIRONMENT:-dev}
        PACKAGE: task-processor
    command: /app/scripts/run_task_processor.sh
    volumes:
      - ~/.aws/:/home/<USER>/.aws:ro
    environment:
      - env_name=${ENVIRONMENT:-dev}
      - AWS_ENDPOINT_URL=http://localstack:4566
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
    env_file:
      - ./app/task_processor/.env
    networks:
      - my_network
    healthcheck:
      test: [ "CMD", "pgrep", "-f", "task_processor" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    depends_on:
      - llm_gateway
      - postgres_with_pgvector
      - localstack
  llm_gateway:
    build:
     context: .
     dockerfile: ./app/llm_gateway/Dockerfile.local
     args:
       Environment: ${ENVIRONMENT:-dev}
    volumes:
      - ~/.aws/:/root/.aws:ro
    ports:
      - "8008:8008"
    environment:
      - env_name=${ENVIRONMENT:-dev}
      - AWS_ENDPOINT_URL=http://localstack:4566
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
    env_file:
      - ./app/llm_gateway/.env
    healthcheck:
      test: [ "CMD", "python", "-c", "import requests; exit() if requests.get('http://localhost:8008/health/readiness').status_code == 200 else exit(1)" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    depends_on:
      - postgres_with_pgvector
      - localstack
    networks:
      - my_network
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
      - "4510-4559:4510-4559"
    volumes:
      - ./localstack-setup.sh:/etc/localstack/init/ready.d/script.sh
      - /tmp/data/localstack:/var/lib/localstack
    environment:
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
      - DEBUG=${DEBUG:-0}
    networks:
      - my_network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://127.0.0.1:4566/_localstack/health" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
  shell:
    image: local_encore:latest
    build:
      context: .
      dockerfile: ./app/api/Dockerfile.local
      args:
        Environment: ${ENVIRONMENT:-dev}
        PACKAGE: api
    ports:
      - "8080:8080"
    volumes:
      - ~/.aws/:/home/<USER>/.aws:ro
    networks:
      - my_network
    depends_on:
      - postgres_with_pgvector
      - task_processor
      - localstack
    env_file:
      - ./app/api/.env
    environment:
      - env_name=${ENVIRONMENT:-dev}
      - AWS_ENDPOINT_URL=http://localstack:4566
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1

volumes:
  postgres-db:


networks:
  my_network:
    driver: bridge
