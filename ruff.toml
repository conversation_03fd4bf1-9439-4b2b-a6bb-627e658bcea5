preview = true

# The formatter wraps lines at a length of 88.
line-length = 140

exclude = [
    "./app/llm_gateway/src/llm_gateway/custom/patches",
]

[lint.flake8-annotations]
allow-star-arg-any = true

[lint]
pylint.max-args = 15 # PLR0913
pylint.max-locals = 30 # PLR0914
pylint.max-statements = 75 # PLR0915
pycodestyle.max-line-length = 140 # E501

extend-select = [
  # "PLC0103",  # invalid-name
  # "PLC0104",  # disallowed-name
  # "PLC0105",  # typevar-name-incorrect-variance
  "D419",  # empty-docstring
  # "PLC0114",  # missing-module-docstring
  # "PLC0115",  # missing-class-docstring
  # "PLC0116",  # missing-function-docstring
  # "PLC0117",  # unnecessary-negation
  # "PLC0121",  # singleton-comparison
  # "PLC0123",  # unidiomatic-typecheck
  # "PLC0131",  # typevar-double-variance
  # "PLC0132",  # typevar-name-mismatch
  # "PLC0200",  # consider-using-enumerate
  # "PLC0201",  # consider-iterating-dictionary
  # "PLC0202",  # bad-classmethod-argument
  # "PLC0203",  # bad-mcs-method-argument
  # "PLC0204",  # bad-mcs-classmethod-argument
  # "PLC0205",  # single-string-used-for-slots
  # "PLC0206",  # consider-using-dict-items
  # "PLC0207",  # use-maxsplit-arg
  # "PLC0208",  # use-sequence-for-iteration
  # "PLC0209",  # consider-using-f-string
  #--> "E501",  # line-too-long
  # "PLC0302",  # too-many-lines
  "W291",  # trailing-whitespace
  # "PLC0304",  # missing-final-newline
  # "PLC0305",  # trailing-newlines
  # "PLC0321",  # multiple-statements
  # "PLC0325",  # superfluous-parens
  # "PLC0327",  # mixed-line-endings
  # "PLC0328",  # unexpected-line-ending-format
  # "PLC0401",  # wrong-spelling-in-comment
  # "PLC0402",  # wrong-spelling-in-docstring
  # "PLC0403",  # invalid-characters-in-docstring
  # "PLC0410",  # multiple-imports
  # "PLC0411",  # wrong-import-order
  # "PLC0412",  # ungrouped-imports
  # "PLC0413",  # wrong-import-position
  "PLC0414",  # useless-import-alias
  # "PLC0415",  # import-outside-toplevel
  # "PLC1802",  # use-implicit-booleaness-not-len
  # "PLC1803",  # use-implicit-booleaness-not-comparison
  "PLC2401",  # non-ascii-name
  # "PLC2403",  # non-ascii-module-import
  # "PLC2503",  # bad-file-encoding
  "PLC2801",  # unnecessary-dunder-call
  # "PLC3001",  # unnecessary-lambda-assignment
  "PLC3002",  # unnecessary-direct-lambda-call
  # "PLE0011",  # unrecognized-inline-option
  # "PLE0013",  # bad-plugin-value
  # "PLE0014",  # bad-configuration-section
  # "PLE0015",  # unrecognized-option
  # "PLE0100",  # init-is-generator
  "PLE0101",  # return-in-init
  # "PLE0102",  # function-redefined
  # "PLE0103",  # not-in-loop
  "F706",  # return-outside-function
  "F704",  # yield-outside-function
  # "PLE0107",  # nonexistent-operator
  # "PLE0108",  # duplicate-argument-name
  # "PLE0110",  # abstract-class-instantiated
  # "PLE0111",  # bad-reversed-sequence
  # "PLE0112",  # too-many-star-expressions
  # "PLE0113",  # invalid-star-assignment-target
  # "PLE0114",  # star-needs-assignment-target
  "PLE0115",  # nonlocal-and-global
  "PLE0116",  # continue-in-finally
  "PLE0117",  # nonlocal-without-binding
  # "PLE0118",  # used-prior-global-declaration
  # "PLE0119",  # misplaced-format-function
  # "PLE0202",  # method-hidden
  # "PLE0203",  # access-member-before-definition
  # "PLE0211",  # no-method-argument
  # "PLE0213",  # no-self-argument
  # "PLE0236",  # invalid-slots-object
  # "PLE0237",  # assigning-non-slot
  # "PLE0238",  # invalid-slots
  # "PLE0239",  # inherit-non-class
  # "PLE0240",  # inconsistent-mro
  "PLE0241",  # duplicate-bases
  # "PLE0242",  # class-variable-slots-conflict
  # "PLE0243",  # invalid-class-object
  # "PLE0244",  # invalid-enum-extension
  # "PLE0245",  # declare-non-slot
  # "PLE0301",  # non-iterator-returned
  "PLE0302",  # unexpected-special-method-signature
  # "PLE0303",  # invalid-length-returned
  # "PLE0304",  # invalid-bool-returned
  # "PLE0305",  # invalid-index-returned
  # "PLE0306",  # invalid-repr-returned
  # "PLE0307",  # invalid-str-returned
  # "PLE0308",  # invalid-bytes-returned
  # "PLE0309",  # invalid-hash-returned
  # "PLE0310",  # invalid-length-hint-returned
  # "PLE0311",  # invalid-format-returned
  # "PLE0312",  # invalid-getnewargs-returned
  # "PLE0313",  # invalid-getnewargs-ex-returned
  # "PLE0401",  # import-error
  # "PLE0402",  # relative-beyond-top-level
  # "PLE0601",  # used-before-assignment
  # "PLE0602",  # undefined-variable
  # "PLE0603",  # undefined-all-variable
  "PLE0604",  # invalid-all-object
  "PLE0605",  # invalid-all-format
  # "PLE0606",  # possibly-used-before-assignment
  # "PLE0611",  # no-name-in-module
  # "PLE0633",  # unpacking-non-sequence
  "PLE0643",  # potential-index-error
  # "PLE0701",  # bad-except-order
  # "PLE0702",  # raising-bad-type
  "PLE0704",  # misplaced-bare-raise
  # "PLE0705",  # bad-exception-cause
  # "PLE0710",  # raising-non-exception
  # "PLE0711",  # notimplemented-raised
  # "PLE0712",  # catching-non-exception
  # "PLE1003",  # bad-super-call
  # "PLE1101",  # no-member
  # "PLE1102",  # not-callable
  # "PLE1111",  # assignment-from-no-return
  # "PLE1120",  # no-value-for-parameter
  # "PLE1121",  # too-many-function-args
  # "PLE1123",  # unexpected-keyword-arg
  # "PLE1124",  # redundant-keyword-arg
  # "PLE1125",  # missing-kwoa
  # "PLE1126",  # invalid-sequence-index
  # "PLE1127",  # invalid-slice-index
  # "PLE1128",  # assignment-from-none
  # "PLE1129",  # not-context-manager
  # "PLE1130",  # invalid-unary-operand-type
  # "PLE1131",  # unsupported-binary-operation
  # "PLE1132",  # repeated-keyword
  # "PLE1133",  # not-an-iterable
  # "PLE1134",  # not-a-mapping
  # "PLE1135",  # unsupported-membership-test
  # "PLE1136",  # unsubscriptable-object
  # "PLE1137",  # unsupported-assignment-operation
  # "PLE1138",  # unsupported-delete-operation
  # "PLE1139",  # invalid-metaclass
  "PLE1141",  # dict-iter-missing-items
  "PLE1142",  # await-outside-async
  # "PLE1143",  # unhashable-member
  # "PLE1144",  # invalid-slice-step
  # "PLE1200",  # logging-unsupported-format
  # "PLE1201",  # logging-format-truncated
  "PLE1205",  # logging-too-many-args
  "PLE1206",  # logging-too-few-args
  # "PLE1300",  # bad-format-character
  # "PLE1301",  # truncated-format-string
  # "PLE1302",  # mixed-format-string
  # "PLE1303",  # format-needs-mapping
  # "PLE1304",  # missing-format-string-key
  # "PLE1305",  # too-many-format-args
  # "PLE1306",  # too-few-format-args
  "PLE1307",  # bad-string-format-type
  "PLE1310",  # bad-str-strip-call
  "PLE1507",  # invalid-envvar-value
  "PLE1519",  # singledispatch-method
  "PLE1520",  # singledispatchmethod-function
  # "PLE1700",  # yield-inside-async-function
  # "PLE1701",  # not-async-context-manager
  # "PLE2501",  # invalid-unicode-codec
  "PLE2502",  # bidirectional-unicode
  "PLE2510",  # invalid-character-backspace
  # "PLE2511",  # invalid-character-carriage-return
  "PLE2512",  # invalid-character-sub
  "PLE2513",  # invalid-character-esc
  "PLE2514",  # invalid-character-nul
  "PLE2515",  # invalid-character-zero-width-space
  # "PLE3102",  # positional-only-arguments-expected
  # "PLE3701",  # invalid-field-call
  # "PLE4702",  # modified-iterating-dict
  "PLE4703",  # modified-iterating-set
  # "PLF0001",  # fatal
  # "PLF0002",  # astroid-error
  # "PLF0010",  # parse-error
  # "PLF0011",  # config-parse-error
  # "PLF0202",  # method-check-failed
  # "PLI1101",  # c-extension-no-member
  # "PLR0022",  # useless-option-value
  # "PLR0123",  # literal-comparison
  "PLR0124",  # comparison-with-itself
  # "PLR0133",  # comparison-of-constants
  "PLR0202",  # no-classmethod-decorator
  "PLR0203",  # no-staticmethod-decorator
  "UP004",  # useless-object-inheritance
  "PLR0206",  # property-with-parameters
  # "PLR0401",  # cyclic-import
  # "PLR0402",  # consider-using-from-import
  # "PLR0801",  # duplicate-code
  # "PLR0902",  # too-many-instance-attributes
  # "PLR0903",  # too-few-public-methods
  "PLR0904",  # too-many-public-methods
  "PLR0911",  # too-many-return-statements
  #--> "PLR0912",  # too-many-branches
  #--> "PLR0913",  # too-many-arguments
  #--> "PLR0914",  # too-many-locals
  #--> "PLR0915",  # too-many-statements
  "PLR0916",  # too-many-boolean-expressions
  "PLR0917",  # too-many-positional-arguments
  # "PLR1701",  # consider-merging-isinstance
  "PLR1702",  # too-many-nested-blocks
  # "PLR1703",  # simplifiable-if-statement
  "PLR1704",  # redefined-argument-from-local
  # "PLR1705",  # no-else-return
  # "PLR1706",  # consider-using-ternary
  # "PLR1707",  # trailing-comma-tuple
  # "PLR1708",  # stop-iteration-return
  # "PLR1709",  # simplify-boolean-expression
  # "PLR1710",  # inconsistent-return-statements
  "PLR1711",  # useless-return
  # "PLR1712",  # consider-swap-variables
  # "PLR1713",  # consider-using-join
  # "PLR1714",  # consider-using-in
  # "PLR1715",  # consider-using-get
  # "PLR1716",  # chained-comparison
  # "PLR1717",  # consider-using-dict-comprehension
  # "PLR1718",  # consider-using-set-comprehension
  # "PLR1719",  # simplifiable-if-expression
  # "PLR1720",  # no-else-raise
  "C416",  # unnecessary-comprehension
  # "PLR1722",  # consider-using-sys-exit
  # "PLR1723",  # no-else-break
  # "PLR1724",  # no-else-continue
  # "PLR1725",  # super-with-arguments
  # "PLR1726",  # simplifiable-condition
  # "PLR1727",  # condition-evals-to-constant
  # "PLR1728",  # consider-using-generator
  # "PLR1729",  # use-a-generator
  # "PLR1730",  # consider-using-min-builtin
  # "PLR1731",  # consider-using-max-builtin
  # "PLR1732",  # consider-using-with
  "PLR1733",  # unnecessary-dict-index-lookup
  # "PLR1734",  # use-list-literal
  # "PLR1735",  # use-dict-literal
  "PLR1736",  # unnecessary-list-index-lookup
  # "PLR1737",  # use-yield-from
  # "PLW0012",  # unknown-option-value
  # "PLW0101",  # unreachable
  # "PLW0102",  # dangerous-default-value
  # "PLW0104",  # pointless-statement
  # "PLW0105",  # pointless-string-statement
  # "PLW0106",  # expression-not-assigned
  # "PLW0107",  # unnecessary-pass
  "PLW0108",  # unnecessary-lambda
  # "PLW0109",  # duplicate-key
  "PLW0120",  # useless-else-on-loop
  # "PLW0122",  # exec-used
  # "PLW0123",  # eval-used
  # "PLW0124",  # confusing-with-statement
  # "PLW0125",  # using-constant-test
  # "PLW0126",  # missing-parentheses-for-call-in-test
  "PLW0127",  # self-assigning-variable
  "PLW0128",  # redeclared-assigned-name
  "PLW0129",  # assert-on-string-literal
  "B033",  # duplicate-value
  "PLW0131",  # named-expr-without-context
  # "PLW0133",  # pointless-exception-statement
  # "PLW0134",  # return-in-finally
  # "PLW0135",  # contextmanager-generator-missing-cleanup
  # "PLW0143",  # comparison-with-callable
  # "PLW0150",  # lost-exception
  "PLW0177",  # nan-comparison
  # "PLW0199",  # assert-on-tuple
  # "PLW0201",  # attribute-defined-outside-init
  "PLW0211",  # bad-staticmethod-argument
  # "PLW0212",  # protected-access
  # "PLW0213",  # implicit-flag-alias
  # "PLW0221",  # arguments-differ
  # "PLW0222",  # signature-differs
  # "PLW0223",  # abstract-method
  # "PLW0231",  # super-init-not-called
  # "PLW0233",  # non-parent-init-called
  # "PLW0236",  # invalid-overridden-method
  # "PLW0237",  # arguments-renamed
  # "PLW0238",  # unused-private-member
  # "PLW0239",  # overridden-final-method
  # "PLW0240",  # subclassed-final-class
  "PLW0244",  # redefined-slots-in-subclass
  "PLW0245",  # super-without-brackets
  # "PLW0246",  # useless-parent-delegation
  # "PLW0301",  # unnecessary-semicolon
  # "PLW0311",  # bad-indentation
  # "PLW0401",  # wildcard-import
  # "PLW0404",  # reimported
  "PLW0406",  # import-self
  # "PLW0407",  # preferred-module
  # "PLW0410",  # misplaced-future
  # "PLW0416",  # shadowed-import
  # "PLW0511",  # fixme
  # "PLW0601",  # global-variable-undefined
  "PLW0602",  # global-variable-not-assigned
  "PLW0603",  # global-statement
  "PLW0604",  # global-at-module-level
  "F401",  # unused-import
  "F841",  # unused-variable
  # "PLW0613",  # unused-argument
  # "PLW0614",  # unused-wildcard-import
  # "PLW0621",  # redefined-outer-name
  # "PLW0622",  # redefined-builtin
  # "PLW0631",  # undefined-loop-variable
  # "PLW0632",  # unbalanced-tuple-unpacking
  # "PLW0640",  # cell-var-from-loop
  # "PLW0641",  # possibly-unused-variable
  # "PLW0642",  # self-cls-assignment
  # "PLW0644",  # unbalanced-dict-unpacking
  "E722",  # bare-except
  # "PLW0705",  # duplicate-except
  # "PLW0706",  # try-except-raise
  # "PLW0707",  # raise-missing-from
  "PLW0711",  # binary-op-exception
  # "PLW0715",  # raising-format-tuple
  # "PLW0716",  # wrong-exception-operation
  # "PLW0718",  # broad-exception-caught
  # "PLW0719",  # broad-exception-raised
  # "PLW1113",  # keyword-arg-before-vararg
  # "PLW1114",  # arguments-out-of-order
  # "PLW1115",  # non-str-assignment-to-dunder-name
  # "PLW1116",  # isinstance-second-argument-not-valid-type
  # "PLW1117",  # kwarg-superseded-by-positional-arg
  # "PLW1201",  # logging-not-lazy
  # "PLW1202",  # logging-format-interpolation
  # "PLW1203",  # logging-fstring-interpolation
  # "PLW1300",  # bad-format-string-key
  # "PLW1301",  # unused-format-string-key
  # "PLW1302",  # bad-format-string
  # "PLW1303",  # missing-format-argument-key
  # "PLW1304",  # unused-format-string-argument
  # "PLW1305",  # format-combined-specification
  # "PLW1306",  # missing-format-attribute
  # "PLW1307",  # invalid-format-index
  # "PLW1308",  # duplicate-string-formatting-argument
  # "PLW1309",  # f-string-without-interpolation
  # "PLW1310",  # format-string-without-interpolation
  # "PLW1401",  # anomalous-backslash-in-string
  # "PLW1402",  # anomalous-unicode-escape-in-string
  # "PLW1404",  # implicit-str-concat
  # "PLW1405",  # inconsistent-quotes
  # "PLW1406",  # redundant-u-string-prefix
  "PLW1501",  # bad-open-mode
  # "PLW1503",  # redundant-unittest-assert
  # "PLW1506",  # bad-thread-instantiation
  "PLW1507",  # shallow-copy-environ
  "PLW1508",  # invalid-envvar-default
  "PLW1509",  # subprocess-popen-preexec-fn
  # "PLW1510",  # subprocess-run-check
  "PLW1514",  # unspecified-encoding
  # "PLW1515",  # forgotten-debug-statement
  # "PLW1518",  # method-cache-max-size-none
  "PLW2101",  # useless-with-lock
  # "PLW2301",  # unnecessary-ellipsis
  # "PLW2402",  # non-ascii-file-name
  # "PLW2601",  # using-f-string-in-unsupported-version
  # "PLW2602",  # using-final-decorator-in-unsupported-version
  # "PLW2603",  # using-exception-groups-in-unsupported-version
  # "PLW2604",  # using-generic-type-syntax-in-unsupported-version
  # "PLW2605",  # using-assignment-expression-in-unsupported-version
  # "PLW2606",  # using-positional-only-args-in-unsupported-version
  # "PLW3101",  # missing-timeout
  "PLW3301",  # nested-min-max
  # "PLW3601",  # bad-chained-comparison
  # "PLW4701",  # modified-iterating-list
  # "PLW4901",  # deprecated-module
  # "PLW4902",  # deprecated-method
  # "PLW4903",  # deprecated-argument
  # "PLW4904",  # deprecated-class
  # "PLW4905",  # deprecated-decorator
  # "PLW4906",  # deprecated-attribute
]

ignore = [
  # "PLC1804",  # use-implicit-booleaness-not-comparison-to-string
  # "PLC1805",  # use-implicit-booleaness-not-comparison-to-zero
  # "PLI0001",  # raw-checker-failed
  # "PLI0010",  # bad-inline-option
  # "PLI0011",  # locally-disabled
  # "PLI0013",  # file-ignored
  # "PLI0020",  # suppressed-message
  # "PLI0021",  # useless-suppression
  # "PLI0022",  # deprecated-pragma
  # "PLI0023",  # use-symbolic-message-instead
  # "PLR0901",  # too-many-ancestors
]
